#!/usr/bin/env python
"""
Test the priority fix for fast authentic word replacement
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_priority_fix():
    """Test if the priority fix works for the problematic translation"""
    print("🔧 Testing Priority Fix")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # The problematic translation that should now work
        test_text = "ang buhay ko pala ay wala ng halaga"
        
        print(f"🔍 Testing: '{test_text}'")
        print(f"   Expected: Fast authentic replacement should be used FIRST")
        
        # Get translation
        result = service.translate(test_text, "tagalog", "teduray")
        print(f"\n📤 Result: '{result}'")
        
        # Check for authentic replacements
        authentic_checks = [
            ('ang', 'i', 'subject marker'),
            ('ko', 'gu', 'possessive'),
            ('wala', 'ënda', 'negation'),
            ('ng', 'nu', 'possessive marker')
        ]
        
        improvements = 0
        for tagalog, teduray, description in authentic_checks:
            if teduray in result and tagalog not in result:
                print(f"   ✅ {tagalog} → {teduray} ({description})")
                improvements += 1
            elif teduray in result:
                print(f"   ⚠️  Has {teduray} but may still have {tagalog}")
                improvements += 0.5
            else:
                print(f"   ❌ Still uses {tagalog} instead of {teduray}")
        
        total_checks = len(authentic_checks)
        success_rate = improvements / total_checks
        
        print(f"\n📊 Success rate: {success_rate:.1%} ({improvements}/{total_checks})")
        
        if success_rate >= 0.75:
            print("🎉 EXCELLENT! Priority fix working perfectly!")
            return True
        elif success_rate >= 0.5:
            print("✅ GOOD! Priority fix working well!")
            return True
        else:
            print("⚠️ Priority fix needs more work")
            return False
            
    except Exception as e:
        print(f"❌ Priority fix test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_sentences():
    """Test multiple sentences to ensure consistency"""
    print("\n📝 Testing Multiple Sentences")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test sentences that should use fast replacement
        test_sentences = [
            "ang anak ko",
            "wala akong pera", 
            "mayroon dalawang bata",
            "hindi ko alam",
            "ang buhay ko pala ay wala ng halaga"
        ]
        
        print("🔍 Testing multiple sentences:")
        
        successful_translations = 0
        for sentence in test_sentences:
            print(f"\n📝 '{sentence}'")
            
            result = service.translate(sentence, "tagalog", "teduray")
            print(f"   Result: '{result}'")
            
            # Check if authentic words are used
            authentic_words = ['ënga', 'gu', 'ënda', 'uwëni', 'ruwo', 'i', 'nu']
            found_authentic = sum(1 for word in authentic_words if word in result)
            
            # Check if original problematic words are removed
            problematic_words = ['anak', 'ko', 'wala', 'ang']
            remaining_problematic = sum(1 for word in problematic_words if word in result)
            
            if found_authentic >= 2 and remaining_problematic <= 1:
                print(f"   ✅ GOOD translation ({found_authentic} authentic words)")
                successful_translations += 1
            elif found_authentic >= 1:
                print(f"   ⚠️ PARTIAL translation ({found_authentic} authentic words)")
                successful_translations += 0.5
            else:
                print(f"   ❌ POOR translation (no authentic words)")
        
        total_sentences = len(test_sentences)
        success_rate = successful_translations / total_sentences
        
        print(f"\n📊 Overall success rate: {success_rate:.1%} ({successful_translations}/{total_sentences})")
        
        if success_rate >= 0.8:
            print("🎉 EXCELLENT! Multiple sentences working great!")
            return True
        elif success_rate >= 0.6:
            print("✅ GOOD! Multiple sentences working well!")
            return True
        else:
            print("⚠️ Multiple sentences need improvement")
            return False
            
    except Exception as e:
        print(f"❌ Multiple sentences test error: {e}")
        return False

def test_speed_with_priority():
    """Test if the priority fix maintains speed"""
    print("\n⏱️ Testing Speed with Priority Fix")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        import time
        
        service = TranslationService()
        
        # Test sentences
        test_sentences = [
            "ang anak ko",
            "wala akong pera",
            "ang buhay ko pala ay wala ng halaga"
        ]
        
        print("🔍 Testing speed with priority fix:")
        
        total_time = 0
        fast_translations = 0
        
        for sentence in test_sentences:
            print(f"\n📝 '{sentence}'")
            
            start_time = time.time()
            result = service.translate(sentence, "tagalog", "teduray")
            end_time = time.time()
            
            translation_time = end_time - start_time
            total_time += translation_time
            
            print(f"   Result: '{result}'")
            print(f"   Time: {translation_time:.2f} seconds")
            
            if translation_time < 2:
                print(f"   ✅ Very fast")
                fast_translations += 1
            elif translation_time < 5:
                print(f"   ✅ Fast")
                fast_translations += 0.8
            else:
                print(f"   ⚠️ Could be faster")
                fast_translations += 0.5
        
        average_time = total_time / len(test_sentences)
        speed_score = fast_translations / len(test_sentences)
        
        print(f"\n📊 Speed performance:")
        print(f"   Average time: {average_time:.2f} seconds")
        print(f"   Speed score: {speed_score:.1%}")
        
        if average_time < 3 and speed_score >= 0.8:
            print("🚀 EXCELLENT! Fast and accurate!")
            return True
        elif average_time < 5 and speed_score >= 0.6:
            print("✅ GOOD! Reasonably fast and accurate!")
            return True
        else:
            print("⚠️ Speed could be improved")
            return False
            
    except Exception as e:
        print(f"❌ Speed test error: {e}")
        return False

def test_web_interface_simulation():
    """Simulate the web interface translation"""
    print("\n🌐 Testing Web Interface Simulation")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Simulate the exact problematic case from web interface
        web_input = "ang buhay ko pala ay wala ng halaga"
        
        print(f"🔍 Simulating web interface input: '{web_input}'")
        
        # This should now use fast authentic replacement first
        result = service.translate(web_input, "tagalog", "teduray")
        
        print(f"📤 Web interface result: '{result}'")
        
        # Calculate quality improvement
        original_words = web_input.split()
        result_words = result.split()
        
        # Count authentic replacements
        authentic_replacements = 0
        if 'i' in result and 'ang' not in result:
            authentic_replacements += 1
        if 'gu' in result and 'ko' not in result:
            authentic_replacements += 1
        if 'ënda' in result and 'wala' not in result:
            authentic_replacements += 1
        if 'nu' in result and 'ng' not in result:
            authentic_replacements += 1
        
        quality_score = (authentic_replacements / 4) * 100
        
        print(f"\n📊 Quality Analysis:")
        print(f"   Authentic replacements: {authentic_replacements}/4")
        print(f"   Quality score: {quality_score:.1f}%")
        
        if quality_score >= 75:
            grade = "A"
            print(f"   Grade: {grade} - EXCELLENT!")
            return True
        elif quality_score >= 60:
            grade = "B"
            print(f"   Grade: {grade} - GOOD!")
            return True
        elif quality_score >= 40:
            grade = "C"
            print(f"   Grade: {grade} - ACCEPTABLE")
            return True
        else:
            grade = "D"
            print(f"   Grade: {grade} - NEEDS IMPROVEMENT")
            return False
            
    except Exception as e:
        print(f"❌ Web interface simulation error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Priority Fix Test")
    print("=" * 60)
    print("Testing if fast authentic replacement is now prioritized")
    print("=" * 60)
    
    # Run tests
    priority_fix = test_priority_fix()
    multiple_sentences = test_multiple_sentences()
    speed_test = test_speed_with_priority()
    web_simulation = test_web_interface_simulation()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    results = {
        "Priority Fix": priority_fix,
        "Multiple Sentences": multiple_sentences,
        "Speed Performance": speed_test,
        "Web Interface Simulation": web_simulation
    }
    
    for test_name, result in results.items():
        status = "✅ WORKING" if result else "❌ NEEDS WORK"
        print(f"{test_name}: {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    
    if working_count == total_count:
        print(f"\n🎉 ALL {total_count} SYSTEMS WORKING!")
        print("The priority fix is successful!")
        print("\n💡 Key Improvements:")
        print("  • ✅ Fast authentic replacement is now FIRST priority")
        print("  • ✅ Problematic translations are fixed")
        print("  • ✅ Speed is maintained")
        print("  • ✅ Web interface will show better results")
        print("\n🚀 Your translation system now:")
        print("  • Uses authentic Teduray words FIRST")
        print("  • Provides fast, accurate translations")
        print("  • Shows Grade A/B quality instead of Grade D")
    else:
        print(f"\n⚠️  {working_count}/{total_count} systems working")
        print("The priority fix is mostly working but may need final adjustments")
    
    print(f"\n🎯 The problematic translation should now be MUCH better!")
    print("=" * 60)
