# Core Django Framework
Django>=5.2.0

# Google Generative AI (Gemini)
google-generativeai>=0.3.0

# Web scraping and HTTP requests
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Data processing
pandas>=2.0.0
numpy>=1.24.0

# Caching (Redis support - optional but recommended for production)
redis>=4.5.0
django-redis>=5.2.0

# Database support (additional databases if needed)
psycopg2-binary>=2.9.0  # PostgreSQL support
mysqlclient>=2.2.0      # MySQL support

# Development and testing
pytest>=7.4.0
pytest-django>=4.5.0
coverage>=7.2.0

# Code quality
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0

# Environment management
python-decouple>=3.8
python-dotenv>=1.0.0

# Additional utilities
Pillow>=10.0.0          # Image processing (if needed)
celery>=5.3.0           # Task queue (for background processing)
django-extensions>=3.2.0  # Django development utilities

# API documentation (optional)
djangorestframework>=3.14.0
drf-spectacular>=0.26.0

# Monitoring and logging (optional)
sentry-sdk>=1.32.0

# Performance optimization
django-debug-toolbar>=4.2.0  # Development only
django-silk>=5.0.0           # Performance profiling

# Translation and internationalization
django-modeltranslation>=0.18.0  # Model translation support

# File handling
openpyxl>=3.1.0         # Excel file support
python-magic>=0.4.27    # File type detection

# Security
django-cors-headers>=4.3.0  # CORS support
django-ratelimit>=4.1.0     # Rate limiting

# Search and indexing (optional for advanced search)
whoosh>=2.7.4
django-haystack>=3.2.1

# Machine learning and NLP (optional for advanced features)
scikit-learn>=1.3.0
nltk>=3.8.0
spacy>=3.6.0

# Web search API
google-api-python-client>=2.100.0  # Google Custom Search API
serpapi>=0.1.5                     # SerpAPI client

# Async support
channels>=4.0.0         # WebSocket support
channels-redis>=4.1.0   # Redis channel layer

# Production deployment
gunicorn>=21.2.0        # WSGI server
whitenoise>=6.5.0       # Static file serving
django-storages>=1.14.0 # Cloud storage support

# Backup and data export
django-dbbackup>=4.0.0

# Admin interface enhancements
django-admin-interface>=0.26.0
django-grappelli>=3.0.0

# Form handling
django-crispy-forms>=2.0
crispy-bootstrap5>=0.7

# Time zone handling
pytz>=2023.3

# JSON handling
orjson>=3.9.0           # Fast JSON library

# HTTP client with better features
httpx>=0.24.0

# Configuration validation
pydantic>=2.0.0

# Markdown support (for documentation)
Markdown>=3.4.0
django-markdownx>=4.0.0

# Email handling
django-anymail>=10.1.0

# Social authentication (optional)
django-allauth>=0.54.0

# API throttling and caching
django-cachalot>=2.6.0

# Background task monitoring
flower>=2.0.0           # Celery monitoring

# Development server enhancements
django-livereload-server>=0.4.0

# Database migrations
django-migration-testcase>=1.0.0

# Testing utilities
factory-boy>=3.3.0     # Test data generation
faker>=19.6.0           # Fake data generation

# Code documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# Performance monitoring
django-prometheus>=2.3.0

# Health checks
django-health-check>=3.17.0

# Feature flags
django-waffle>=4.1.0

# Content management
django-ckeditor>=6.7.0

# File uploads
django-cleanup>=8.0.0

# URL shortening (optional)
django-shortuuidfield>=0.1.3

# Pagination
django-pure-pagination>=0.3.0

# Timezone utilities
django-timezone-field>=5.1

# Model utilities
django-model-utils>=4.3.0

# Environment-specific settings
django-environ>=0.11.0

# Task scheduling
django-crontab>=0.7.1

# Image optimization
pillow-simd>=10.0.0     # Faster image processing

# Memory profiling (development)
memory-profiler>=0.61.0

# Network utilities
dnspython>=2.4.0

# Compression
django-compressor>=4.4

# Session management
django-user-sessions>=2.0.0

# Audit logging
django-simple-history>=3.4.0

# Content security
django-csp>=3.7

# Rate limiting
django-axes>=6.1.0      # Brute force protection

# Backup utilities
django-backup>=1.0.0

# Development tools
ipython>=8.15.0         # Enhanced Python shell
jupyter>=1.0.0          # Notebook support

# Profiling
py-spy>=0.3.14          # Python profiler
line-profiler>=4.1.0    # Line-by-line profiling
