# Core Django Framework
Django>=4.2.0

# Google Generative AI (Gemini)
google-generativeai>=0.3.0

# Web scraping and HTTP requests
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Data processing (optional - only if you need advanced data analysis)
# pandas>=2.0.0
# numpy>=1.24.0

# Caching (Redis support - optional but recommended for production)
# redis>=4.5.0
# django-redis>=5.2.0

# Database support (only install what you need)
# psycopg2-binary>=2.9.0  # PostgreSQL support
# mysqlclient>=2.2.0      # MySQL support

# Development and testing (optional)
# pytest>=7.4.0
# pytest-django>=4.5.0
# coverage>=7.2.0

# Environment management
python-decouple>=3.8
python-dotenv>=1.0.0

# Web search API
serpapi>=0.1.5                     # SerpAPI client

# Time zone handling
pytz>=2023.3

# HTTP client with better features
httpx>=0.24.0

# Development tools (optional)
# ipython>=8.15.0         # Enhanced Python shell
