"""
Hierarchical Translation System
Handles translation at different linguistic levels: paragraphs, sentences, phrases, and words.
Maintains proper grammar structure instead of word-by-word translation.
"""

import re
from collections import defaultdict
from django.db.models import Q
from .models import TranslationRule


class HierarchicalTranslator:
    """
    Translates text by understanding linguistic hierarchy:
    1. Paragraph level - Overall context and flow
    2. Sentence level - Complete thoughts and grammar
    3. Phrase level - Meaningful chunks (noun phrases, verb phrases)
    4. Word level - Individual words (only as last resort)
    """
    
    def __init__(self):
        self.phrase_patterns = self._load_phrase_patterns()
        self.grammar_rules = self._load_grammar_rules()
        self.sentence_patterns = self._load_sentence_patterns()
        
    def translate_hierarchically(self, text, source_lang, target_lang, context_rules=None):
        """
        Main hierarchical translation method that processes text at multiple levels.
        
        Args:
            text: Input text to translate
            source_lang: Source language
            target_lang: Target language
            context_rules: Database rules for context
            
        Returns:
            Dictionary with translation and analysis information
        """
        # Determine the linguistic level of the input
        text_level = self._determine_text_level(text)
        
        if text_level == 'paragraph':
            return self._translate_paragraph(text, source_lang, target_lang, context_rules)
        elif text_level == 'sentence':
            return self._translate_sentence(text, source_lang, target_lang, context_rules)
        elif text_level == 'phrase':
            return self._translate_phrase(text, source_lang, target_lang, context_rules)
        else:  # word level
            return self._translate_word(text, source_lang, target_lang, context_rules)
    
    def _determine_text_level(self, text):
        """Determine the linguistic level of the input text"""
        # Count sentences (by periods, question marks, exclamation marks)
        sentences = re.split(r'[.!?]+', text.strip())
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if len(sentences) > 1:
            return 'paragraph'
        
        # Count words
        words = text.split()
        
        if len(words) > 8:
            return 'sentence'
        elif len(words) > 1:
            return 'phrase'
        else:
            return 'word'
    
    def _translate_paragraph(self, text, source_lang, target_lang, context_rules):
        """Translate at paragraph level - maintain coherence across sentences"""
        try:
            # Split into sentences
            sentences = re.split(r'([.!?]+)', text)
            translated_parts = []
            paragraph_context = self._build_paragraph_context(text, source_lang)
            
            i = 0
            while i < len(sentences):
                sentence = sentences[i].strip()
                punctuation = sentences[i + 1] if i + 1 < len(sentences) else ''
                
                if sentence:
                    # Translate sentence with paragraph context
                    sentence_result = self._translate_sentence(
                        sentence, source_lang, target_lang, context_rules, paragraph_context
                    )
                    
                    if sentence_result and sentence_result['translation']:
                        translated_parts.append(sentence_result['translation'] + punctuation)
                    else:
                        translated_parts.append(sentence + punctuation)
                
                i += 2  # Skip punctuation
            
            final_translation = ' '.join(translated_parts).strip()
            
            return {
                'translation': final_translation,
                'level': 'paragraph',
                'method': 'hierarchical_paragraph',
                'coherence_score': self._calculate_coherence_score(translated_parts),
                'sentence_count': len([s for s in sentences if s.strip()])
            }
            
        except Exception as e:
            print(f"Paragraph translation error: {e}")
            return None
    
    def _translate_sentence(self, text, source_lang, target_lang, context_rules, paragraph_context=None):
        """Translate at sentence level - maintain grammatical structure"""
        try:
            # Analyze sentence structure
            sentence_analysis = self._analyze_sentence_structure(text, source_lang)
            
            # Check for complete sentence patterns first
            sentence_pattern_result = self._match_sentence_patterns(text, source_lang, target_lang)
            if sentence_pattern_result:
                return {
                    'translation': sentence_pattern_result,
                    'level': 'sentence',
                    'method': 'sentence_pattern',
                    'structure': sentence_analysis
                }
            
            # Break into meaningful phrases
            phrases = self._segment_into_phrases(text, source_lang)
            translated_phrases = []
            
            for phrase_info in phrases:
                phrase_result = self._translate_phrase(
                    phrase_info['text'], source_lang, target_lang, context_rules
                )
                
                if phrase_result and phrase_result['translation']:
                    translated_phrases.append({
                        'translation': phrase_result['translation'],
                        'type': phrase_info['type'],
                        'original': phrase_info['text']
                    })
                else:
                    # Fallback to word-by-word for this phrase
                    word_result = self._translate_phrase_words(
                        phrase_info['text'], source_lang, target_lang
                    )
                    translated_phrases.append({
                        'translation': word_result,
                        'type': phrase_info['type'],
                        'original': phrase_info['text']
                    })
            
            # Reconstruct sentence with proper grammar
            final_translation = self._reconstruct_sentence(
                translated_phrases, sentence_analysis, target_lang
            )
            
            return {
                'translation': final_translation,
                'level': 'sentence',
                'method': 'hierarchical_sentence',
                'structure': sentence_analysis,
                'phrases': translated_phrases
            }
            
        except Exception as e:
            print(f"Sentence translation error: {e}")
            return None
    
    def _translate_phrase(self, text, source_lang, target_lang, context_rules):
        """Translate at phrase level - meaningful chunks"""
        try:
            # Check for exact phrase matches first
            phrase_match = self._find_phrase_match(text, source_lang, target_lang)
            if phrase_match:
                return {
                    'translation': phrase_match,
                    'level': 'phrase',
                    'method': 'phrase_match'
                }
            
            # Analyze phrase type
            phrase_type = self._identify_phrase_type(text, source_lang)
            
            # Apply phrase-specific translation rules
            if phrase_type == 'noun_phrase':
                translation = self._translate_noun_phrase(text, source_lang, target_lang)
            elif phrase_type == 'verb_phrase':
                translation = self._translate_verb_phrase(text, source_lang, target_lang)
            elif phrase_type == 'prepositional_phrase':
                translation = self._translate_prepositional_phrase(text, source_lang, target_lang)
            elif phrase_type == 'question_phrase':
                translation = self._translate_question_phrase(text, source_lang, target_lang)
            else:
                # Generic phrase translation
                translation = self._translate_generic_phrase(text, source_lang, target_lang)
            
            return {
                'translation': translation,
                'level': 'phrase',
                'method': f'{phrase_type}_translation',
                'phrase_type': phrase_type
            }
            
        except Exception as e:
            print(f"Phrase translation error: {e}")
            return None
    
    def _translate_word(self, text, source_lang, target_lang, context_rules):
        """Translate at word level - last resort"""
        try:
            # Simple word lookup
            word_match = self._find_word_match(text, source_lang, target_lang)
            
            return {
                'translation': word_match or text,
                'level': 'word',
                'method': 'word_lookup'
            }
            
        except Exception as e:
            print(f"Word translation error: {e}")
            return {'translation': text, 'level': 'word', 'method': 'fallback'}
    
    def _analyze_sentence_structure(self, text, source_lang):
        """Analyze the grammatical structure of a sentence"""
        analysis = {
            'type': 'declarative',  # declarative, interrogative, imperative, exclamatory
            'tense': 'present',     # past, present, future
            'voice': 'active',      # active, passive
            'mood': 'indicative',   # indicative, subjunctive, imperative
            'has_subject': True,
            'has_object': False,
            'word_order': 'SVO'     # SVO, VSO, SOV, etc.
        }
        
        # Detect question
        if text.strip().endswith('?') or any(qw in text.lower() for qw in ['ano', 'sino', 'saan', 'kailan', 'bakit', 'paano']):
            analysis['type'] = 'interrogative'
        
        # Detect imperative (commands)
        imperative_markers = ['mag', 'um', 'in', 'an']
        if any(text.lower().startswith(marker) for marker in imperative_markers):
            analysis['mood'] = 'imperative'
        
        # Detect tense markers
        if any(marker in text.lower() for marker in ['nag', 'um', 'in']):
            analysis['tense'] = 'past'
        elif any(marker in text.lower() for marker in ['mag', 'ma', 'mang']):
            analysis['tense'] = 'future'
        
        # Detect object presence
        if any(marker in text.lower() for marker in ['ng', 'sa', 'kay']):
            analysis['has_object'] = True
        
        return analysis
    
    def _segment_into_phrases(self, text, source_lang):
        """Segment sentence into meaningful phrases"""
        phrases = []
        
        # Simple phrase segmentation based on markers
        # This can be enhanced with more sophisticated NLP
        
        words = text.split()
        current_phrase = []
        current_type = 'generic'
        
        phrase_boundaries = ['ng', 'sa', 'kay', 'para', 'dahil', 'kasi', 'at', 'o']
        
        for i, word in enumerate(words):
            if word.lower() in phrase_boundaries and current_phrase:
                # End current phrase
                phrases.append({
                    'text': ' '.join(current_phrase),
                    'type': current_type,
                    'position': len(phrases)
                })
                current_phrase = [word]
                current_type = self._determine_phrase_type_from_marker(word.lower())
            else:
                current_phrase.append(word)
        
        # Add final phrase
        if current_phrase:
            phrases.append({
                'text': ' '.join(current_phrase),
                'type': current_type,
                'position': len(phrases)
            })
        
        return phrases
    
    def _identify_phrase_type(self, text, source_lang):
        """Identify the type of phrase"""
        text_lower = text.lower()
        
        # Question phrases
        question_words = ['ano', 'sino', 'saan', 'kailan', 'bakit', 'paano', 'kumusta']
        if any(qw in text_lower for qw in question_words):
            return 'question_phrase'
        
        # Verb phrases (action-oriented)
        verb_markers = ['mag', 'nag', 'um', 'in', 'an', 'kumain', 'uminom', 'natulog']
        if any(vm in text_lower for vm in verb_markers):
            return 'verb_phrase'
        
        # Prepositional phrases
        prepositions = ['sa', 'ng', 'kay', 'para', 'dahil', 'mula']
        if any(prep in text_lower for prep in prepositions):
            return 'prepositional_phrase'
        
        # Noun phrases (default for descriptive content)
        return 'noun_phrase'
    
    def _translate_noun_phrase(self, text, source_lang, target_lang):
        """Translate noun phrases with proper structure"""
        # Teduray typically follows: Determiner + Adjective + Noun
        # Tagalog: ang/ng + adjective + noun
        
        words = text.split()
        translated_words = []
        
        for word in words:
            # Translate each word but maintain noun phrase structure
            translation = self._find_word_match(word, source_lang, target_lang)
            translated_words.append(translation or word)
        
        # Apply target language noun phrase rules
        if target_lang.lower() == 'teduray':
            return self._apply_teduray_noun_phrase_rules(translated_words)
        else:
            return ' '.join(translated_words)
    
    def _translate_verb_phrase(self, text, source_lang, target_lang):
        """Translate verb phrases with proper conjugation"""
        # Handle verb conjugation and aspect
        
        words = text.split()
        main_verb = None
        verb_modifiers = []
        
        # Identify main verb and modifiers
        for word in words:
            if self._is_verb(word, source_lang):
                main_verb = word
            else:
                verb_modifiers.append(word)
        
        if main_verb:
            # Translate verb with proper conjugation
            verb_translation = self._translate_verb_with_aspect(main_verb, source_lang, target_lang)
            
            # Translate modifiers
            modifier_translations = []
            for modifier in verb_modifiers:
                mod_trans = self._find_word_match(modifier, source_lang, target_lang)
                modifier_translations.append(mod_trans or modifier)
            
            # Reconstruct verb phrase
            if target_lang.lower() == 'teduray':
                return self._apply_teduray_verb_phrase_rules(verb_translation, modifier_translations)
            else:
                return ' '.join([verb_translation] + modifier_translations)
        
        # Fallback to word-by-word
        return self._translate_phrase_words(text, source_lang, target_lang)
    
    def _translate_question_phrase(self, text, source_lang, target_lang):
        """Translate question phrases with proper question structure"""
        text_lower = text.lower()
        
        # Common question patterns
        question_patterns = {
            'kumusta ka': 'kumusta kew',
            'ano ang': 'unu go',
            'sino ang': 'sinew go',
            'saan ka': 'diin kew',
            'kailan ka': 'kanu kew',
            'bakit ka': 'nguda kew'
        }
        
        # Check for exact question patterns
        for tagalog_q, teduray_q in question_patterns.items():
            if tagalog_q in text_lower:
                return text_lower.replace(tagalog_q, teduray_q)
        
        # Generic question word translation
        words = text.split()
        translated_words = []
        
        for word in words:
            word_lower = word.lower()
            if word_lower in ['ano', 'sino', 'saan', 'kailan', 'bakit', 'paano']:
                # Translate question words
                q_translations = {
                    'ano': 'unu',
                    'sino': 'sinew',
                    'saan': 'diin',
                    'kailan': 'kanu',
                    'bakit': 'nguda',
                    'paano': 'piye'
                }
                translated_words.append(q_translations.get(word_lower, word))
            else:
                translation = self._find_word_match(word, source_lang, target_lang)
                translated_words.append(translation or word)
        
        return ' '.join(translated_words)
    
    def _reconstruct_sentence(self, translated_phrases, sentence_analysis, target_lang):
        """Reconstruct sentence with proper target language grammar"""
        if not translated_phrases:
            return ""
        
        # Extract translations
        phrase_translations = [p['translation'] for p in translated_phrases]
        
        # Apply target language sentence structure rules
        if target_lang.lower() == 'teduray':
            return self._apply_teduray_sentence_structure(phrase_translations, sentence_analysis)
        else:
            return ' '.join(phrase_translations)
    
    def _apply_teduray_sentence_structure(self, phrases, analysis):
        """Apply Teduray sentence structure rules"""
        # Teduray tends to be VSO (Verb-Subject-Object) in some contexts
        # But can also be SVO depending on focus
        
        if analysis['type'] == 'interrogative':
            # Question structure: Question word + verb + subject + object
            return ' '.join(phrases)
        elif analysis['mood'] == 'imperative':
            # Command structure: Verb + object
            return ' '.join(phrases)
        else:
            # Declarative: Subject + verb + object (with proper markers)
            return ' '.join(phrases)
    
    def _find_phrase_match(self, text, source_lang, target_lang):
        """Find exact phrase matches in database"""
        try:
            # Look for exact phrase matches
            exact_match = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__iexact=text.strip()
            ).first()
            
            if exact_match:
                return exact_match.target_text
            
            # Look for partial phrase matches
            partial_matches = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__icontains=text.strip()
            ).order_by('-source_text__length')[:3]
            
            for match in partial_matches:
                if len(match.source_text.split()) > 1:  # Multi-word phrases
                    return match.target_text
            
            return None
            
        except Exception:
            return None
    
    def _find_word_match(self, word, source_lang, target_lang):
        """Find single word translation"""
        try:
            match = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__iexact=word.strip()
            ).first()
            
            return match.target_text if match else None
            
        except Exception:
            return None
    
    def _load_phrase_patterns(self):
        """Load common phrase patterns"""
        return {
            'greetings': ['kumusta', 'hello', 'hi', 'good morning', 'good afternoon'],
            'farewells': ['paalam', 'goodbye', 'see you', 'bye'],
            'courtesy': ['salamat', 'thank you', 'please', 'excuse me', 'sorry'],
            'family': ['ama', 'ina', 'anak', 'kuya', 'ate', 'lolo', 'lola'],
            'time': ['umaga', 'tanghali', 'hapon', 'gabi', 'ngayon', 'bukas', 'kahapon']
        }
    
    def _load_grammar_rules(self):
        """Load grammar transformation rules"""
        return {
            'teduray': {
                'word_order': 'VSO_flexible',
                'markers': ['i', 'de', 'go', 'nu', 'ku'],
                'verb_prefixes': ['me', 'ne', 'te'],
                'noun_markers': ['i', 'de']
            },
            'tagalog': {
                'word_order': 'VSO_VOS',
                'markers': ['ang', 'ng', 'sa'],
                'verb_prefixes': ['mag', 'nag', 'um', 'in'],
                'noun_markers': ['ang', 'ng']
            }
        }
    
    def _load_sentence_patterns(self):
        """Load common sentence patterns"""
        return {
            'tagalog_to_teduray': {
                'kumusta ka': 'kumusta kew',
                'salamat po': 'salamat',
                'mahal kita': 'malago taku',
                'kumain ka na': 'menama kew na',
                'magandang umaga': 'mefiya umaga'
            }
        }
    
    # Helper methods for linguistic analysis
    def _is_verb(self, word, source_lang):
        """Check if word is a verb"""
        verb_indicators = {
            'tagalog': ['mag', 'nag', 'um', 'in', 'an', 'kumain', 'uminom', 'natulog'],
            'teduray': ['me', 'ne', 'te', 'menama', 'minum', 'meturug']
        }
        
        word_lower = word.lower()
        indicators = verb_indicators.get(source_lang.lower(), [])
        
        return any(word_lower.startswith(ind) or word_lower == ind for ind in indicators)
    
    def _translate_phrase_words(self, text, source_lang, target_lang):
        """Fallback word-by-word translation for phrases"""
        words = text.split()
        translated_words = []
        
        for word in words:
            translation = self._find_word_match(word, source_lang, target_lang)
            translated_words.append(translation or word)
        
        return ' '.join(translated_words)
    
    def _calculate_coherence_score(self, translated_parts):
        """Calculate coherence score for paragraph translation"""
        if len(translated_parts) <= 1:
            return 1.0
        
        # Simple coherence measure based on consistency
        # This can be enhanced with more sophisticated metrics
        return 0.8  # Placeholder
    
    def _build_paragraph_context(self, text, source_lang):
        """Build context for paragraph-level translation"""
        return {
            'topic': self._identify_topic(text),
            'tone': self._identify_tone(text),
            'domain': self._identify_domain(text)
        }
    
    def _identify_topic(self, text):
        """Identify the main topic of the text"""
        # Simple keyword-based topic identification
        topics = {
            'family': ['ama', 'ina', 'anak', 'pamilya', 'kuya', 'ate'],
            'food': ['kumain', 'pagkain', 'tinapay', 'tubig', 'kain'],
            'greeting': ['kumusta', 'hello', 'magandang', 'umaga'],
            'religion': ['diyos', 'panginoon', 'dasal', 'simbahan']
        }
        
        text_lower = text.lower()
        for topic, keywords in topics.items():
            if any(keyword in text_lower for keyword in keywords):
                return topic
        
        return 'general'
    
    def _identify_tone(self, text):
        """Identify the tone of the text"""
        if '?' in text:
            return 'questioning'
        elif '!' in text:
            return 'exclamatory'
        else:
            return 'neutral'
    
    def _identify_domain(self, text):
        """Identify the domain/context of the text"""
        domains = {
            'formal': ['po', 'opo', 'pakisuyo'],
            'informal': ['ka', 'ikaw', 'tayo'],
            'religious': ['panginoon', 'diyos', 'amen'],
            'family': ['ama', 'ina', 'anak']
        }
        
        text_lower = text.lower()
        for domain, indicators in domains.items():
            if any(indicator in text_lower for indicator in indicators):
                return domain
        
        return 'general'

    # Missing helper methods
    def _apply_teduray_noun_phrase_rules(self, translated_words):
        """Apply Teduray-specific noun phrase rules"""
        if not translated_words:
            return ""

        # Simple rule: add appropriate marker if missing
        result = ' '.join(translated_words)

        # If it starts with a noun and no marker, add 'i'
        if not any(result.startswith(marker) for marker in ['i ', 'de ', 'go ']):
            result = 'i ' + result

        return result

    def _apply_teduray_verb_phrase_rules(self, verb_translation, modifier_translations):
        """Apply Teduray-specific verb phrase rules"""
        if not verb_translation:
            return ' '.join(modifier_translations)

        # Combine verb with modifiers
        all_parts = [verb_translation] + modifier_translations
        return ' '.join(all_parts)

    def _translate_verb_with_aspect(self, verb, source_lang, target_lang):
        """Translate verb with proper aspect/tense"""
        verb_translation = self._find_word_match(verb, source_lang, target_lang)

        if verb_translation:
            return verb_translation

        # Handle common verb patterns
        if source_lang.lower() == 'tagalog' and target_lang.lower() == 'teduray':
            verb_mappings = {
                'kumain': 'menama',
                'uminom': 'minum',
                'natulog': 'meturug',
                'gumising': 'mebangon',
                'tumakbo': 'melayag',
                'lumakad': 'melakad'
            }
            return verb_mappings.get(verb.lower(), verb)

        return verb

    def _translate_prepositional_phrase(self, text, source_lang, target_lang):
        """Translate prepositional phrases"""
        words = text.split()
        translated_words = []

        for word in words:
            if word.lower() in ['sa', 'ng', 'kay', 'para', 'dahil']:
                prep_translations = {
                    'sa': 'de',
                    'ng': 'nu',
                    'kay': 'kay',
                    'para': 'para',
                    'dahil': 'sebab'
                }
                translated_words.append(prep_translations.get(word.lower(), word))
            else:
                translation = self._find_word_match(word, source_lang, target_lang)
                translated_words.append(translation or word)

        return ' '.join(translated_words)

    def _translate_generic_phrase(self, text, source_lang, target_lang):
        """Generic phrase translation"""
        return self._translate_phrase_words(text, source_lang, target_lang)

    def _match_sentence_patterns(self, text, source_lang, target_lang):
        """Match complete sentence patterns"""
        patterns = self.sentence_patterns.get(f"{source_lang}_to_{target_lang}", {})

        text_lower = text.lower().strip()

        # Check for exact matches
        for pattern, translation in patterns.items():
            if pattern in text_lower:
                return text_lower.replace(pattern, translation)

        return None

    def _determine_phrase_type_from_marker(self, marker):
        """Determine phrase type from grammatical marker"""
        marker_types = {
            'ng': 'noun_phrase',
            'sa': 'prepositional_phrase',
            'kay': 'prepositional_phrase',
            'para': 'prepositional_phrase',
            'dahil': 'prepositional_phrase',
            'at': 'conjunction',
            'o': 'conjunction'
        }

        return marker_types.get(marker, 'generic')
