import google.generativeai as genai
import time
import requests
import os
import re
import csv
import json
import hashlib
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.db.models import Q
from .models import TranslationRule, TranslationHistory, TranslationCorrection

class TranslationService:
    def __init__(self):
        genai.configure(api_key=settings.GEMINI_API_KEY)
        self.model = genai.GenerativeModel('models/gemini-1.5-flash')
        self.max_retries = 3
        self.retry_delay = 5  # seconds

        # Initialize professional components
        from .translation_memory import TranslationMemory
        from .web_search_service import WebSearchService
        from .quality_scorer import TranslationQualityScorer
        from .cache_manager import IntelligentCacheManager
        from .attention_translator import AttentionTranslator
        from .hierarchical_translator import HierarchicalTranslator

        self.translation_memory = TranslationMemory()
        self.web_search = WebSearchService()
        self.quality_scorer = TranslationQualityScorer()
        self.cache_manager = IntelligentCacheManager()
        self.attention_translator = AttentionTranslator()
        self.hierarchical_translator = HierarchicalTranslator()

        # Load data sources
        self.translate_py_data = self._load_translate_py_data()
        self.translate_py_corpus = self._load_translate_py_corpus()
        # Use existing Bible logic instead of CSV
        self._bible_cache = {}

        # Performance tracking
        self.translation_stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'gemini_calls': 0,
            'web_searches': 0
        }

    def _get_bible_books(self):
        """Get list of available Bible books using existing logic"""
        # Use the existing Bible book list from your system
        bible_books = [
            'Mateo', 'Markos', 'Lukas', 'Juan', 'Rénigoy de Apostol', 'Roma',
            '1 Korinto', '2 Korinto', 'Galasia', 'Efeso', 'Filipos', 'Kolosas',
            '1 Tesalonika', '2 Tesalonika', '1 Timoteo', '2 Timoteo', 'Tito',
            'Filemon', 'De Judio', 'Santiago', '1 Pedro', '2 Pedro',
            '1 Juan', '2 Juan', '3 Juan', 'Judas', 'Kéfégétigan'
        ]
        return bible_books

    def _lookup_teduray_bible_verse(self, book, chapter, verse):
        """Return Teduray verse using existing Bible logic."""
        try:
            # Use the existing Bible verse fetching logic with correct parameters
            return self._fetch_bible_verse(book, chapter, verse, 'teduray')
        except Exception as e:
            print(f"Error looking up Bible verse: {e}")
            return None

    def _load_translate_py_corpus(self):
        # Load Tagalog↔Teduray pairs from translate.py (tab-separated, skip header lines)
        corpus = []
        try:
            with open(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'translate.py'), encoding='utf-8') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '\t' in line:
                        parts = line.strip().split('\t')
                        if len(parts) == 3:
                            tagalog, _, teduray = parts
                            corpus.append({'tagalog': tagalog.strip(), 'teduray': teduray.strip()})
        except Exception as e:
            print(f"Error loading translate.py corpus: {e}")
        return corpus

    def _find_translate_py_match(self, text, source_lang, target_lang):
        # Direct match in translate.py corpus (case-insensitive, flexible punctuation)
        norm = lambda s: re.sub(r'[^\w\s]', '', s.lower())
        text_norm = norm(text)
        for entry in self.translate_py_corpus:
            src = entry.get(source_lang, '').strip()
            tgt = entry.get(target_lang, '').strip()
            if norm(src) == text_norm:
                return tgt
        return None

    def translate(self, text, source_lang, target_lang):
        """
        Professional translation method with intelligent caching, quality scoring, and multi-source integration.

        Translation Pipeline:
        1. Check intelligent cache for high-quality cached translations
        2. Check Bible corpus for exact verse matches
        3. Check translate.py for authoritative translations
        4. Check database for exact/flexible matches
        5. Use Gemini AI with comprehensive context and web search
        6. Score and cache the result
        """
        self.translation_stats['total_requests'] += 1

        # Step 1: Check intelligent cache first
        cached_result = self.cache_manager.get_translation(text, source_lang, target_lang)
        if cached_result and cached_result.get('quality_score', 0) >= 0.7:
            self.translation_stats['cache_hits'] += 1
            return cached_result['translation']

        # Step 2: Check Bible corpus for exact matches
        bible_result = self._check_bible_corpus(text, source_lang, target_lang)
        if bible_result:
            self._cache_and_return(text, source_lang, target_lang, bible_result,
                                 quality_score=0.95, source='bible_corpus')
            return bible_result

        # Step 3: Check translate.py for authoritative translations
        py_result = self._check_translate_py(text, source_lang, target_lang)
        if py_result:
            self._cache_and_return(text, source_lang, target_lang, py_result,
                                 quality_score=0.90, source='translate_py')
            return py_result

        # Step 4: Check database for exact/flexible matches (only for simple words/phrases)
        # BUT verify with web search and Gemini for accuracy
        db_result = self._check_database_matches(text, source_lang, target_lang)
        if db_result and self._is_high_quality_match(text, db_result) and self._is_simple_text(text):
            # Verify database result with web search and Gemini
            verified_result = self._verify_translation_with_web_and_gemini(text, db_result, source_lang, target_lang)
            if verified_result:
                self._cache_and_return(text, source_lang, target_lang, verified_result,
                                     quality_score=0.85, source='database_verified')
                return verified_result
            else:
                # Database result not verified, continue to other methods
                pass

        # Step 5: Use hierarchical translation (sentence/phrase/word level)
        hierarchical_result = self._hierarchical_translate(text, source_lang, target_lang)
        if hierarchical_result:
            # Also verify hierarchical results for complex sentences
            if not self._is_simple_text(text):
                verified_hierarchical = self._verify_translation_with_web_and_gemini(
                    text, hierarchical_result, source_lang, target_lang
                )
                if verified_hierarchical:
                    return verified_hierarchical
            return hierarchical_result

        # Step 6: Web search + Gemini for authentic translations
        web_gemini_result = self._web_search_gemini_translate(text, source_lang, target_lang)
        if web_gemini_result:
            # Learn from this authentic translation
            self._learn_from_authentic_translation(text, web_gemini_result, source_lang, target_lang)
            return web_gemini_result

        # Step 7: Handle unknown/unfamiliar words with targeted web search
        unknown_words_result = self._handle_unknown_words(text, source_lang, target_lang)
        if unknown_words_result:
            # Learn from this translation
            self._learn_from_authentic_translation(text, unknown_words_result, source_lang, target_lang)
            return unknown_words_result

        # Step 8: Use attention-enhanced AI translation
        attention_result = self._attention_enhanced_translate(text, source_lang, target_lang)
        if attention_result:
            return attention_result

        # Step 9: Fallback to professional AI translation with enhanced context
        ai_result = self._professional_ai_translate_enhanced(text, source_lang, target_lang)
        if ai_result:
            return ai_result

        # Step 10: Last resort - return error message
        return f"[Translation not available] {text}"

    def _check_bible_corpus(self, text, source_lang, target_lang):
        """Check Bible corpus for exact verse matches using existing Bible logic"""
        try:
            # Try to parse if text looks like a Bible reference (e.g., "John 3:16")
            import re
            verse_pattern = r'(\w+)\s+(\d+):(\d+)'
            match = re.match(verse_pattern, text.strip())

            if match:
                book, chapter, verse = match.groups()
                bible_result = self._fetch_bible_verse(book, int(chapter), int(verse), target_lang)
                if bible_result:
                    return bible_result

            # Also check cached Bible verses
            cached_verse = self.cache_manager.get_bible_verse(text, 1, 1, target_lang)
            if cached_verse:
                return cached_verse.get('verse_text')

            return None
        except Exception as e:
            print(f"Error checking Bible corpus: {e}")
            return None

    def _check_translate_py(self, text, source_lang, target_lang):
        """Check translate.py for authoritative translations"""
        try:
            # Use the existing method but with better error handling
            result = self._find_translate_py_match(text, source_lang.lower(), target_lang.lower())
            if result:
                return self._preserve_punctuation(text, result)
            return None
        except Exception as e:
            print(f"Error checking translate.py: {e}")
            return None

    def _check_database_matches(self, text, source_lang, target_lang):
        """Check database for exact and flexible matches"""
        try:
            # Use existing flexible match method
            match = self._find_flexible_match(text, source_lang)
            if match:
                return self._preserve_punctuation(text, match.target_text)
            return None
        except Exception as e:
            print(f"Error checking database: {e}")
            return None

    def _is_high_quality_match(self, original_text, translated_text):
        """Determine if a database match is high quality"""
        if not translated_text or len(translated_text.strip()) == 0:
            return False

        # Check if it's not just a word-by-word replacement
        orig_words = set(original_text.lower().split())
        trans_words = set(translated_text.lower().split())

        # If more than 70% of words are different, it's likely a good translation
        overlap = len(orig_words & trans_words)
        if len(orig_words) > 0:
            difference_ratio = 1 - (overlap / len(orig_words))
            return difference_ratio >= 0.3

        return True

    def _is_simple_text(self, text):
        """Check if text is simple enough for database-only translation"""
        # Simple text: 1-2 words, no complex sentence structure
        words = text.split()

        # Single words are simple
        if len(words) <= 1:
            return True

        # Two-word phrases might be simple
        if len(words) == 2:
            return True

        # Complex sentences should use hierarchical translation
        if len(words) > 2:
            return False

        return True

    def _hierarchical_translate(self, text, source_lang, target_lang):
        """Hierarchical translation that handles sentences, phrases, and words appropriately"""
        try:
            self.translation_stats['gemini_calls'] += 1

            # Get context rules from database
            context_rules = TranslationRule.objects.filter(
                source_language=source_lang
            )[:30]  # More rules for hierarchical analysis

            # Use hierarchical translation
            hierarchical_result = self.hierarchical_translator.translate_hierarchically(
                text, source_lang, target_lang, context_rules
            )

            if hierarchical_result and hierarchical_result['translation']:
                translation = hierarchical_result['translation']

                # Enhanced quality scoring based on translation level
                quality_data = self.quality_scorer.score_translation(
                    text, translation, source_lang, target_lang,
                    translation_source=f"hierarchical_{hierarchical_result['level']}"
                )

                # Boost quality score based on translation level
                level_bonus = {
                    'paragraph': 0.15,
                    'sentence': 0.10,
                    'phrase': 0.05,
                    'word': 0.0
                }

                bonus = level_bonus.get(hierarchical_result['level'], 0.0)
                quality_data['overall_score'] = min(quality_data['overall_score'] + bonus, 1.0)

                # Cache the result with hierarchical metadata
                self._cache_and_return(
                    text, source_lang, target_lang, translation,
                    quality_score=quality_data['overall_score'],
                    source=f"hierarchical_{hierarchical_result['level']}"
                )

                return translation

            return None

        except Exception as e:
            print(f"Hierarchical translation failed: {e}")
            return None

    def _handle_unknown_words(self, text, source_lang, target_lang):
        """Handle unknown/unfamiliar words with targeted web search and learning"""
        try:
            # Identify unknown words
            unknown_words = self._identify_unknown_words(text, source_lang)

            if not unknown_words:
                return None

            print(f"Found unknown words: {unknown_words}")

            # Search for Teduray translations of unknown words
            teduray_translations = {}
            for word in unknown_words:
                teduray_result = self._search_teduray_translation(word, source_lang, target_lang)
                if teduray_result:
                    teduray_translations[word] = teduray_result
                    print(f"Found Teduray translation: '{word}' → '{teduray_result}'")

            # If we found translations for unknown words, reconstruct the sentence
            if teduray_translations:
                reconstructed = self._reconstruct_with_new_words(text, teduray_translations, source_lang, target_lang)
                if reconstructed:
                    # Cache the new word translations for future use
                    self._cache_new_word_translations(teduray_translations, source_lang, target_lang)
                    return reconstructed

            # If web search didn't find translations, use Gemini to translate unknown words
            if unknown_words:
                gemini_translation = self._gemini_translate_unknown_words(text, unknown_words, source_lang, target_lang)
                if gemini_translation:
                    return gemini_translation

            return None

        except Exception as e:
            print(f"Unknown words handling failed: {e}")
            return None

    def _identify_unknown_words(self, text, source_lang):
        """Identify words that are not in our database or translate.py"""
        import re
        words = re.findall(r'\b\w+\b', text.lower())

        # Get known words from database
        known_words = set()
        db_rules = TranslationRule.objects.filter(source_language=source_lang)
        for rule in db_rules:
            rule_words = re.findall(r'\b\w+\b', rule.source_text.lower())
            known_words.update(rule_words)

        # Get known words from translate.py
        for entry in self.translate_py_data:
            if entry.get('source'):
                py_words = re.findall(r'\b\w+\b', entry['source'].lower())
                known_words.update(py_words)

        # Common words that don't need translation
        common_words = {
            'ang', 'ng', 'sa', 'na', 'at', 'ay', 'ko', 'mo', 'ka', 'ako', 'ikaw', 'siya',
            'kami', 'kayo', 'sila', 'namin', 'natin', 'ninyo', 'nila', 'akin', 'iyo', 'kaniya',
            'amin', 'atin', 'inyo', 'kanila', 'si', 'ni', 'kay', 'para', 'dahil', 'kasi',
            'pero', 'ngunit', 'kaya', 'kundi', 'o', 'at', 'pati', 'din', 'rin', 'man', 'pa'
        }

        # Find unknown words
        unknown = []
        for word in words:
            if (word not in known_words and
                word not in common_words and
                len(word) > 2 and
                not word.isdigit()):
                unknown.append(word)

        return unknown

    def _search_teduray_translation(self, word, source_lang, target_lang):
        """Search specifically for Teduray translations of a word"""
        try:
            # Use web search with Teduray-specific queries
            search_queries = [
                f'"{word}" Teduray translation',
                f'"{word}" Teduray language meaning',
                f'"{word}" Tagalog to Teduray dictionary',
                f'Teduray word for "{word}"',
                f'"{word}" Teduray equivalent'
            ]

            for query in search_queries:
                results = self.web_search.search_translation(word, source_lang, target_lang, 'linguistic')

                # Look for Teduray words in the results
                for result in results:
                    teduray_word = self._extract_teduray_from_result(result, word)
                    if teduray_word:
                        return teduray_word

            return None

        except Exception as e:
            print(f"Teduray search failed for '{word}': {e}")
            return None

    def _extract_teduray_from_result(self, result, original_word):
        """Extract Teduray translation from search result"""
        snippet = result.get('snippet', '').lower()
        title = result.get('title', '').lower()

        # Look for patterns like "word means X in Teduray" or "Teduray: X"
        import re
        patterns = [
            rf'{re.escape(original_word.lower())}[^a-z]*(?:means?|is|translates? to)[^a-z]*([a-z]+)',
            rf'teduray[^a-z]*(?:word|translation)[^a-z]*([a-z]+)',
            rf'([a-z]+)[^a-z]*(?:in teduray|teduray word)',
            rf'teduray[^a-z]*([a-z]+)'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, snippet + ' ' + title)
            for match in matches:
                if len(match) > 2 and match != original_word.lower():
                    return match

        return None

    def _reconstruct_with_new_words(self, text, new_translations, source_lang, target_lang):
        """Reconstruct sentence with newly found word translations"""
        try:
            # Replace unknown words with their Teduray translations
            reconstructed = text
            for tagalog_word, teduray_word in new_translations.items():
                reconstructed = re.sub(rf'\b{re.escape(tagalog_word)}\b', teduray_word, reconstructed, flags=re.IGNORECASE)

            # Now try hierarchical translation on the reconstructed text
            hierarchical_result = self.hierarchical_translator.translate_hierarchically(
                reconstructed, source_lang, target_lang
            )

            if hierarchical_result and hierarchical_result['translation']:
                return hierarchical_result['translation']

            return reconstructed

        except Exception as e:
            print(f"Reconstruction failed: {e}")
            return None

    def _cache_new_word_translations(self, translations, source_lang, target_lang):
        """Cache newly discovered word translations"""
        try:
            for tagalog_word, teduray_word in translations.items():
                # Add to database if not exists
                existing = TranslationRule.objects.filter(
                    source_language=source_lang,
                    source_text__iexact=tagalog_word
                ).first()

                if not existing:
                    TranslationRule.objects.create(
                        source_language=source_lang,
                        source_text=tagalog_word,
                        target_text=teduray_word
                    )
                    print(f"Cached new translation: '{tagalog_word}' → '{teduray_word}'")
        except Exception as e:
            print(f"Caching new translations failed: {e}")

    def _gemini_translate_unknown_words(self, text, unknown_words, source_lang, target_lang):
        """Use Gemini to translate text with unknown words"""
        try:
            print(f"🤖 Using Gemini to translate unknown words: {unknown_words}")

            unknown_words_str = ", ".join(unknown_words)

            prompt = f"""You are a Teduray language expert. Translate this text to authentic Teduray.

ORIGINAL TEXT: "{text}"
UNKNOWN WORDS: {unknown_words_str}

TASK:
1. Translate the entire text to authentic Teduray
2. Pay special attention to the unknown words
3. Use proper Teduray grammar and markers
4. Ensure cultural appropriateness

TEDURAY GRAMMAR RULES:
- Subject marker: i (replaces "ang")
- Possessive/object: nu (replaces "ng")
- Location: de (replaces "sa")
- Conditional: kun (replaces "kung")
- Use authentic Teduray vocabulary

EXAMPLES:
- "kumain ang lalaki" → "menama i lagey"
- "masaya ang bata" → "mefiya i anak"
- "kompyuter" → "kompyuter" (modern words may stay the same)

RESPOND WITH ONLY THE TEDURAY TRANSLATION:"""

            response = self._call_gemini_with_retries(prompt)

            if response and len(response.strip()) > 0:
                cleaned = response.strip().strip('"').strip("'")
                if len(cleaned) > 0 and not cleaned.startswith('['):
                    print(f"🤖 Gemini translated unknown words: '{cleaned}'")
                    return cleaned

            return None

        except Exception as e:
            print(f"❌ Gemini unknown words translation failed: {e}")
            return None

    def _attention_enhanced_translate(self, text, source_lang, target_lang):
        """Attention-enhanced translation using focus mechanisms"""
        try:
            self.translation_stats['gemini_calls'] += 1

            # Get context rules from database
            context_rules = TranslationRule.objects.filter(
                source_language=source_lang
            )[:20]  # Limit for performance

            # Use attention mechanism to analyze and translate
            attention_result = self.attention_translator.translate_with_attention(
                text, source_lang, target_lang, context_rules
            )

            if attention_result and attention_result['translation']:
                translation = attention_result['translation']

                # Enhanced quality scoring with attention information
                quality_data = self.quality_scorer.score_translation(
                    text, translation, source_lang, target_lang,
                    translation_source='attention_enhanced'
                )

                # Boost quality score if attention was well-distributed
                if attention_result['confidence'] > 0.7:
                    quality_data['overall_score'] = min(quality_data['overall_score'] + 0.1, 1.0)

                # Cache the result with attention metadata
                self._cache_and_return(
                    text, source_lang, target_lang, translation,
                    quality_score=quality_data['overall_score'],
                    source='attention_enhanced'
                )

                return translation

            return None

        except Exception as e:
            print(f"Attention-enhanced translation failed: {e}")
            return None

    def _professional_ai_translate_enhanced(self, text, source_lang, target_lang):
        """Enhanced professional AI translation with better context and learning"""
        try:
            self.translation_stats['gemini_calls'] += 1

            # Get comprehensive context
            web_context = self._get_web_search_context(text, source_lang, target_lang)
            similar_translations = self.translation_memory.get_similar_translations(
                text, source_lang, target_lang, limit=5
            )

            # Get unknown words context
            unknown_words = self._identify_unknown_words(text, source_lang)

            # Build enhanced prompt with unknown words handling
            prompt = self._build_enhanced_prompt(
                text, source_lang, target_lang, web_context, similar_translations, unknown_words
            )

            # Get translation from Gemini
            translation = self._call_gemini_with_retries(prompt)

            if translation:
                # Score the translation quality
                quality_data = self.quality_scorer.score_translation(
                    text, translation, source_lang, target_lang,
                    translation_source='gemini_enhanced_v2'
                )

                # Cache the result
                self._cache_and_return(
                    text, source_lang, target_lang, translation,
                    quality_score=quality_data['overall_score'],
                    source='gemini_enhanced_v2'
                )

                return translation

            return None

        except Exception as e:
            print(f"Enhanced professional AI translation failed: {e}")
            return None

    def _build_enhanced_prompt(self, text, source_lang, target_lang, web_context, similar_translations, unknown_words):
        """Build enhanced prompt with unknown words handling"""

        # Analyze text characteristics
        punctuation_info = self._analyze_punctuation(text)

        # Build context sections
        web_context_str = ""
        if web_context:
            web_context_str = "WEB SEARCH CONTEXT:\n" + "\n".join([
                f"• {result.get('title', '')}: {result.get('processed_snippet', '')[:100]}..."
                for result in web_context[:3]
            ])

        similar_context_str = ""
        if similar_translations:
            similar_context_str = "SIMILAR TRANSLATIONS:\n" + "\n".join([
                f"'{rule.source_text}' → '{rule.target_text}'"
                for rule in similar_translations[:5]
            ])

        unknown_words_str = ""
        if unknown_words:
            unknown_words_str = f"UNKNOWN WORDS TO RESEARCH: {', '.join(unknown_words)}\n" + \
                               "Please search your knowledge for Teduray translations of these words."

        prompt = f"""You are a professional {source_lang.title()}-{target_lang.title()} translator with expertise in Teduray language and culture.

CRITICAL INSTRUCTIONS:
1. Provide ONLY the translation, no explanations or meta-text
2. Preserve punctuation: {punctuation_info}
3. Use natural {target_lang.title()} grammar and structure
4. Consider cultural context and appropriateness
5. Focus on meaning, not word-by-word translation
6. Use proper Teduray grammatical markers (i, de, go, nu, etc.)

{web_context_str}

{similar_context_str}

{unknown_words_str}

SPECIFIC TRANSLATION RULES:
- kumain → menama (eat)
- lalaki → lagey (man/male)
- matanda → lukes (old/elder)
- tinapay → fan (bread)
- ang → i (subject marker)
- ng → nu (possessive/object marker)
- sa → de (location/direction marker)

TEDURAY GRAMMAR STRUCTURE:
- Use VSO (Verb-Subject-Object) or SVO order as appropriate
- Add proper markers: i (subject), de (location), nu (possessive)
- Maintain cultural appropriateness

INPUT TEXT: "{text}"

TRANSLATION:"""

        return prompt

    def _get_web_search_context(self, text, source_lang, target_lang):
        """Get web search context specifically for Teduray translations"""
        try:
            # Search for Teduray-specific content
            search_results = self.web_search.search_translation(text, source_lang, target_lang, 'linguistic')

            # Process results to extract useful context
            processed_results = []
            for result in search_results[:3]:  # Limit to top 3 results
                processed_snippet = self._process_search_snippet_for_teduray(result.get('snippet', ''))
                if processed_snippet:
                    processed_results.append({
                        'title': result.get('title', ''),
                        'processed_snippet': processed_snippet,
                        'url': result.get('url', '')
                    })

            return processed_results

        except Exception as e:
            print(f"Web search context failed: {e}")
            return []

    def _process_search_snippet_for_teduray(self, snippet):
        """Process search snippet to extract Teduray-relevant information"""
        if not snippet:
            return ""

        # Look for Teduray words or translation patterns
        teduray_indicators = ['teduray', 'maguindanao', 'mindanao', 'indigenous', 'translation', 'language']

        snippet_lower = snippet.lower()
        if any(indicator in snippet_lower for indicator in teduray_indicators):
            # Extract sentences that contain Teduray-related content
            sentences = snippet.split('.')
            relevant_sentences = []

            for sentence in sentences:
                if any(indicator in sentence.lower() for indicator in teduray_indicators):
                    relevant_sentences.append(sentence.strip())

            return '. '.join(relevant_sentences[:2])  # Limit to 2 sentences

        return ""

    def _verify_translation_with_web_and_gemini(self, original_text, proposed_translation, source_lang, target_lang):
        """Verify a proposed translation using web search and Gemini"""
        try:
            print(f"🔍 Verifying translation: '{original_text}' → '{proposed_translation}'")

            # Step 1: Search for authentic Teduray translations
            web_results = self.web_search.search_translation(original_text, source_lang, target_lang, 'linguistic')

            # Step 2: Extract authentic translations from web results
            authentic_translations = []
            for result in web_results[:3]:
                extracted = self._extract_teduray_from_result(result, original_text)
                if extracted:
                    authentic_translations.extend(extracted)

            # Step 3: Use Gemini to verify and choose the best translation
            if authentic_translations:
                verified_translation = self._gemini_verify_translation(
                    original_text, proposed_translation, authentic_translations, source_lang, target_lang
                )
                if verified_translation:
                    print(f"✅ Verified translation: '{verified_translation}'")
                    return verified_translation

            # Step 4: If no web verification, use Gemini to validate the proposed translation
            gemini_validation = self._gemini_validate_translation(
                original_text, proposed_translation, source_lang, target_lang
            )

            if gemini_validation:
                print(f"✅ Gemini validated: '{gemini_validation}'")
                return gemini_validation

            print(f"⚠️  Could not verify translation")
            return None

        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return None

    def _web_search_gemini_translate(self, text, source_lang, target_lang):
        """Use web search + Gemini for authentic translations"""
        try:
            print(f"🌐 Web search + Gemini translation for: '{text}'")

            # Step 1: Search for authentic Teduray translations
            web_results = self.web_search.search_translation(text, source_lang, target_lang, 'linguistic')

            # Step 2: Extract potential translations
            potential_translations = []
            web_context = []

            for result in web_results[:5]:
                # Extract translations
                extracted = self._extract_teduray_from_result(result, text)
                if extracted:
                    potential_translations.extend(extracted)

                # Collect context
                web_context.append({
                    'title': result.get('title', ''),
                    'snippet': result.get('snippet', ''),
                    'url': result.get('url', '')
                })

            # Step 3: Use Gemini to create authentic translation
            if potential_translations or web_context:
                gemini_result = self._gemini_create_authentic_translation(
                    text, potential_translations, web_context, source_lang, target_lang
                )

                if gemini_result:
                    # Cache the result for future use
                    self._cache_and_return(
                        text, source_lang, target_lang, gemini_result,
                        quality_score=0.90, source='web_gemini_authentic'
                    )

                    # Learn from this translation
                    self._learn_from_authentic_translation(text, gemini_result, source_lang, target_lang)

                    return gemini_result

            return None

        except Exception as e:
            print(f"❌ Web search + Gemini failed: {e}")
            return None

    def _gemini_verify_translation(self, original, proposed, authentic_options, source_lang, target_lang):
        """Use Gemini to verify and choose the best translation"""
        try:
            prompt = f"""You are a Teduray language expert. Please verify and improve this translation.

ORIGINAL {source_lang.upper()}: "{original}"
PROPOSED TRANSLATION: "{proposed}"

AUTHENTIC TEDURAY OPTIONS FOUND:
{chr(10).join([f"• {option}" for option in authentic_options[:5]])}

TASK:
1. Evaluate if the proposed translation is accurate
2. Compare with the authentic options
3. Provide the BEST Teduray translation
4. Use proper Teduray grammar and markers

CRITICAL RULES:
- Use authentic Teduray vocabulary
- Apply correct grammatical markers (i, de, nu, kun, go, etc.)
- Follow natural Teduray sentence structure
- Preserve cultural appropriateness

RESPOND WITH ONLY THE BEST TEDURAY TRANSLATION:"""

            response = self._call_gemini_with_retries(prompt)

            if response and len(response.strip()) > 0:
                # Clean the response
                cleaned = response.strip().strip('"').strip("'")
                if len(cleaned) > 0 and not cleaned.startswith('['):
                    return cleaned

            return None

        except Exception as e:
            print(f"❌ Gemini verification failed: {e}")
            return None

    def _gemini_validate_translation(self, original, proposed, source_lang, target_lang):
        """Use Gemini to validate a translation without web options"""
        try:
            prompt = f"""You are a Teduray language expert. Validate and improve this translation.

ORIGINAL {source_lang.upper()}: "{original}"
PROPOSED TRANSLATION: "{proposed}"

TASK:
1. Check if this is authentic Teduray
2. Verify grammar and vocabulary
3. Improve if needed
4. Use proper Teduray markers and structure

TEDURAY GRAMMAR RULES:
- Subject marker: i (replaces "ang")
- Possessive/object: nu (replaces "ng")
- Location: de (replaces "sa")
- Conditional: kun (replaces "kung")
- Proper verb forms and conjugations

RESPOND WITH ONLY THE CORRECTED TEDURAY TRANSLATION:"""

            response = self._call_gemini_with_retries(prompt)

            if response and len(response.strip()) > 0:
                cleaned = response.strip().strip('"').strip("'")
                if len(cleaned) > 0 and not cleaned.startswith('['):
                    return cleaned

            return None

        except Exception as e:
            print(f"❌ Gemini validation failed: {e}")
            return None

    def _gemini_create_authentic_translation(self, text, potential_translations, web_context, source_lang, target_lang):
        """Use Gemini to create authentic translation from web research"""
        try:
            # Build context from web results
            context_str = ""
            if web_context:
                context_str = "WEB RESEARCH CONTEXT:\n"
                for ctx in web_context[:3]:
                    context_str += f"• {ctx['title']}: {ctx['snippet'][:100]}...\n"

            potential_str = ""
            if potential_translations:
                potential_str = f"POTENTIAL TEDURAY WORDS FOUND:\n{', '.join(set(potential_translations))}\n"

            prompt = f"""You are a Teduray language expert with access to authentic sources.

TRANSLATE TO TEDURAY: "{text}"

{context_str}

{potential_str}

REQUIREMENTS:
1. Use AUTHENTIC Teduray vocabulary and grammar
2. Apply correct grammatical markers:
   - i (subject marker, replaces "ang")
   - nu (possessive/object, replaces "ng")
   - de (location, replaces "sa")
   - kun (conditional, replaces "kung")
3. Follow natural Teduray sentence structure
4. Use proper verb conjugations
5. Ensure cultural appropriateness

EXAMPLES OF CORRECT TEDURAY:
- "kumain ang lalaki" → "menama i lagey"
- "masaya ang bata" → "mefiya i anak"
- "sa bahay" → "de balay"

RESPOND WITH ONLY THE AUTHENTIC TEDURAY TRANSLATION:"""

            response = self._call_gemini_with_retries(prompt)

            if response and len(response.strip()) > 0:
                cleaned = response.strip().strip('"').strip("'")
                if len(cleaned) > 0 and not cleaned.startswith('['):
                    return cleaned

            return None

        except Exception as e:
            print(f"❌ Gemini authentic translation failed: {e}")
            return None

    def _learn_from_authentic_translation(self, original, translation, source_lang, target_lang):
        """Learn from authentic translations to improve our database"""
        try:
            # Add to database if not exists
            existing = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__iexact=original
            ).first()

            if not existing:
                TranslationRule.objects.create(
                    source_language=source_lang,
                    source_text=original,
                    target_text=translation
                )
                print(f"📚 Learned: '{original}' → '{translation}'")

            # Also learn individual words
            original_words = original.split()
            translation_words = translation.split()

            # Simple word alignment for learning
            if len(original_words) == len(translation_words):
                for orig_word, trans_word in zip(original_words, translation_words):
                    if len(orig_word) > 2 and len(trans_word) > 2:
                        existing_word = TranslationRule.objects.filter(
                            source_language=source_lang,
                            source_text__iexact=orig_word
                        ).first()

                        if not existing_word:
                            TranslationRule.objects.create(
                                source_language=source_lang,
                                source_text=orig_word,
                                target_text=trans_word
                            )
                            print(f"📚 Learned word: '{orig_word}' → '{trans_word}'")

        except Exception as e:
            print(f"❌ Learning failed: {e}")

    def _extract_teduray_from_result(self, result, original_word):
        """Extract Teduray translation from search result"""
        snippet = result.get('snippet', '').lower()
        title = result.get('title', '').lower()

        # Look for patterns like "word means X in Teduray" or "Teduray: X"
        import re
        patterns = [
            rf'{re.escape(original_word.lower())}[^a-z]*(?:means?|is|translates? to)[^a-z]*([a-z]+)',
            rf'teduray[^a-z]*(?:word|translation)[^a-z]*([a-z]+)',
            rf'([a-z]+)[^a-z]*(?:in teduray|teduray word)',
            rf'teduray[^a-z]*([a-z]+)',
            rf'"{re.escape(original_word.lower())}"[^"]*teduray[^"]*"([^"]+)"',
            rf'teduray[^"]*"([^"]+)"[^"]*"{re.escape(original_word.lower())}"'
        ]

        extracted = []
        for pattern in patterns:
            matches = re.findall(pattern, snippet + ' ' + title)
            for match in matches:
                if len(match) > 2 and match != original_word.lower():
                    # Filter out common English/Tagalog words
                    if match not in ['the', 'and', 'for', 'with', 'this', 'that', 'from', 'they', 'have', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'would', 'there', 'could', 'other', 'after', 'first', 'well', 'many', 'some', 'what', 'know', 'way', 'been', 'call', 'who', 'oil', 'sit', 'now', 'find', 'long', 'down', 'day', 'did', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'ang', 'mga', 'ako', 'siya', 'kami', 'kayo', 'sila', 'namin', 'natin', 'ninyo', 'nila']:
                        extracted.append(match)

        return extracted[:3]  # Return top 3 potential translations

    def _professional_ai_translate(self, text, source_lang, target_lang):
        """Professional AI translation with web search integration and quality scoring"""
        try:
            self.translation_stats['gemini_calls'] += 1

            # Get web search context
            web_context = self._get_web_search_context(text, source_lang, target_lang)

            # Get similar translations for context
            similar_translations = self.translation_memory.get_similar_translations(
                text, source_lang, target_lang, limit=5
            )

            # Build comprehensive prompt
            prompt = self._build_professional_prompt(
                text, source_lang, target_lang, web_context, similar_translations
            )

            # Get translation from Gemini
            translation = self._call_gemini_with_retries(prompt)

            if translation:
                # Score the translation quality
                quality_data = self.quality_scorer.score_translation(
                    text, translation, source_lang, target_lang,
                    translation_source='gemini_enhanced'
                )

                # Cache the result
                self._cache_and_return(
                    text, source_lang, target_lang, translation,
                    quality_score=quality_data['overall_score'],
                    source='gemini_enhanced'
                )

                return translation

            return None

        except Exception as e:
            print(f"Professional AI translation failed: {e}")
            return None

    def _get_web_search_context(self, text, source_lang, target_lang):
        """Get web search context for translation"""
        try:
            self.translation_stats['web_searches'] += 1

            # Check cache first
            cached_results = self.cache_manager.get_web_search_results(text, 'translation')
            if cached_results:
                return cached_results['results']

            # Perform web search
            search_results = self.web_search.search_translation(
                text, source_lang, target_lang, search_type='general'
            )

            # Cache the results
            self.cache_manager.set_web_search_results(text, 'translation', search_results)

            return search_results[:3]  # Return top 3 results

        except Exception as e:
            print(f"Web search context failed: {e}")
            return []

    def _build_professional_prompt(self, text, source_lang, target_lang, web_context, similar_translations):
        """Build a comprehensive prompt for Gemini translation"""

        # Analyze text characteristics
        punctuation_info = self._analyze_punctuation(text)

        # Build context sections
        web_context_str = ""
        if web_context:
            web_context_str = "WEB SEARCH CONTEXT:\n" + "\n".join([
                f"• {result.get('title', '')}: {result.get('processed_snippet', '')[:100]}..."
                for result in web_context[:3]
            ])

        similar_context_str = ""
        if similar_translations:
            similar_context_str = "SIMILAR TRANSLATIONS:\n" + "\n".join([
                f"'{rule.source_text}' → '{rule.target_text}'"
                for rule in similar_translations[:5]
            ])

        prompt = f"""You are a professional {source_lang.title()}-{target_lang.title()} translator with expertise in Teduray language and culture.

CRITICAL INSTRUCTIONS:
1. Provide ONLY the translation, no explanations or meta-text
2. Preserve punctuation: {punctuation_info}
3. Use natural {target_lang.title()} grammar and structure
4. Consider cultural context and appropriateness
5. Focus on meaning, not word-by-word translation

{web_context_str}

{similar_context_str}

SPECIFIC TRANSLATION RULES:
- kumain → mënama (eat) - NEVER use 'mama' alone
- lalaki → lagëy (man/male)
- matanda → lukës (old/elder)
- tinapay → ëfan (bread)

INPUT TEXT: "{text}"

TRANSLATION:"""

        return prompt

    def _call_gemini_with_retries(self, prompt):
        """Call Gemini API with retry logic and error handling"""
        for attempt in range(self.max_retries):
            try:
                response = self.model.generate_content(prompt)
                if response and response.text:
                    result = response.text.strip()
                    # Clean up response
                    if result.startswith('"') and result.endswith('"'):
                        result = result[1:-1]
                    if result.startswith("'") and result.endswith("'"):
                        result = result[1:-1]
                    return result
                else:
                    raise Exception("Empty response from Gemini")

            except Exception as e:
                error_msg = str(e).lower()
                if ("quota" in error_msg or "rate" in error_msg) and attempt < self.max_retries - 1:
                    print(f"Rate limit hit, waiting {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                    continue
                elif attempt < self.max_retries - 1:
                    print(f"API error: {e}, retrying...")
                    time.sleep(1)
                    continue
                else:
                    print(f"Gemini API failed after {self.max_retries} attempts: {e}")
                    raise e

        return None

    def _cache_and_return(self, text, source_lang, target_lang, translation, quality_score, source):
        """Cache translation result and return it"""
        try:
            translation_data = {
                'translation': translation,
                'quality_score': quality_score,
                'source': source
            }

            # Cache using the cache manager
            self.cache_manager.set_translation(text, source_lang, target_lang, translation_data)

            # Also save to translation memory
            self.translation_memory.cache_translation(
                text, source_lang, target_lang, translation, quality_score, source
            )

        except Exception as e:
            print(f"Error caching translation: {e}")

    def learn_from_correction(self, original_text, source_lang, target_lang,
                            original_translation, corrected_translation):
        """Learn from user corrections to improve future translations"""
        return self.translation_memory.learn_from_correction(
            original_text, source_lang, target_lang,
            original_translation, corrected_translation
        )

    def get_translation_statistics(self):
        """Get comprehensive translation system statistics"""
        memory_stats = self.translation_memory.get_translation_statistics()
        cache_stats = self.cache_manager.get_cache_statistics()

        return {
            'service_stats': self.translation_stats,
            'memory_stats': memory_stats,
            'cache_stats': cache_stats,
            'performance_summary': {
                'cache_hit_rate': cache_stats.get('hit_rate', 0),
                'total_translations': memory_stats.get('total_translations', 0),
                'correction_rate': memory_stats.get('correction_rate', 0),
                'average_quality': cache_stats.get('average_quality', 0)
            }
        }

    def clear_low_quality_cache(self, min_quality=0.3):
        """Clear low-quality cached translations"""
        return self.cache_manager.clear_low_quality_cache(min_quality)

    def export_translation_data(self):
        """Export all translation data for backup or analysis"""
        return {
            'memory_export': self.translation_memory.export_memory(),
            'cache_export': self.cache_manager.export_cache_data(),
            'service_stats': self.translation_stats
        }

    def save_translation_feedback(self, source_text, source_lang, target_lang, translated_text, feedback_type):
        """Save user feedback for translations"""
        try:
            from translation_app.models import TranslationFeedback

            # Create feedback record (timestamp is auto-added)
            feedback = TranslationFeedback.objects.create(
                source_text=source_text,
                source_language=source_lang,
                target_language=target_lang,
                translated_text=translated_text,
                feedback_type=feedback_type
            )

            # Update translation statistics
            self.translation_stats['feedback_received'] = self.translation_stats.get('feedback_received', 0) + 1
            if feedback_type == 'good':
                self.translation_stats['positive_feedback'] = self.translation_stats.get('positive_feedback', 0) + 1
            elif feedback_type == 'bad':
                self.translation_stats['negative_feedback'] = self.translation_stats.get('negative_feedback', 0) + 1

            # If feedback is negative, mark for review/improvement
            if feedback_type == 'bad':
                self._handle_negative_feedback(source_text, translated_text, source_lang, target_lang)

            return feedback

        except Exception as e:
            print(f"Error saving feedback: {e}")
            return None

    def _handle_negative_feedback(self, source_text, translated_text, source_lang, target_lang):
        """Handle negative feedback by marking for improvement"""
        try:
            # Lower the quality score in cache if it exists
            cached_translation = self.cache_manager.get_translation(source_text, source_lang, target_lang)
            if cached_translation:
                # Reduce quality score
                new_quality = max(0.1, cached_translation.get('quality_score', 0.5) - 0.2)
                self.cache_manager.cache_translation(
                    source_text, source_lang, target_lang, translated_text,
                    quality_score=new_quality,
                    source=cached_translation.get('source', 'unknown') + '_feedback_adjusted'
                )
                print(f"Adjusted quality score for '{source_text}' due to negative feedback")

            # Mark for future improvement with web search + Gemini
            # This could trigger a background task to find better translations

        except Exception as e:
            print(f"Error handling negative feedback: {e}")

    def _find_flexible_match(self, text, source_lang):
        """Find database match with flexible punctuation handling"""
        # First try exact match
        exact_match = TranslationRule.objects.filter(
            source_language=source_lang,
            source_text__iexact=text
        ).first()

        if exact_match:
            return exact_match

        # Try with normalized text (remove common punctuation)
        normalized_text = text.strip().rstrip('.,!?;:')
        if normalized_text != text:
            normalized_match = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__iexact=normalized_text
            ).first()

            if normalized_match:
                return normalized_match

        # Try adding common punctuation if input doesn't have it
        if not text.endswith('.'):
            with_period = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__iexact=text + '.'
            ).first()

            if with_period:
                return with_period

        # Try removing punctuation from database entries and comparing
        # This handles cases where database has punctuation but input doesn't
        potential_matches = TranslationRule.objects.filter(
            source_language=source_lang,
            source_text__icontains=normalized_text[:20] if len(normalized_text) > 20 else normalized_text
        )

        for rule in potential_matches:
            rule_normalized = rule.source_text.strip().rstrip('.,!?;:')
            if rule_normalized.lower() == normalized_text.lower():
                return rule

        return None

    def _detect_unfamiliar_words(self, text, all_rules):
        """Detect words that are not in our database and might need web search"""
        # Extract words from text (remove punctuation)
        import re
        words = re.findall(r'\b\w+\b', text.lower())

        # Get all known words from database
        known_words = set()
        for rule in all_rules:
            rule_words = re.findall(r'\b\w+\b', rule.source_text.lower())
            known_words.update(rule_words)

        # Find unfamiliar words (not in database)
        unfamiliar = []
        for word in words:
            # Skip very common words that don't need translation
            common_words = {'ang', 'ng', 'sa', 'na', 'at', 'ay', 'ko', 'mo', 'ka', 'ako', 'ikaw', 'siya',
                        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
                        'i', 'you', 'he', 'she', 'it', 'we', 'they', 'is', 'are', 'was', 'were', 'be', 'been'}

            if word not in common_words and word not in known_words and len(word) > 2:
                unfamiliar.append(word)

        # Return unfamiliar words if they make up significant portion of text
        if len(unfamiliar) > 0 and len(unfamiliar) / len(words) > 0.3:
            return unfamiliar

        return []

    def _preserve_punctuation(self, original_text, translated_text):
        """Preserve punctuation from original text in translation"""
        # Get the punctuation from the original text
        if original_text.endswith('?'):
            if not translated_text.endswith('?'):
                return translated_text.rstrip('.,!') + '?'
        elif original_text.endswith('!'):
            if not translated_text.endswith('!'):
                return translated_text.rstrip('.,?') + '!'
        elif original_text.endswith('.'):
            if not translated_text.endswith('.'):
                return translated_text.rstrip('?!') + '.'

        return translated_text

    def _is_good_translation(self, original, translated):
        """Check if dictionary translation is good enough"""
        if not translated:
            return False

        # Count how many words were actually translated vs kept original
        original_words = set(original.lower().split())
        translated_words = set(translated.lower().split())

        # If more than 50% of words were translated, consider it good
        overlap = len(original_words & translated_words)
        translation_ratio = 1 - (overlap / len(original_words)) if original_words else 0

        return translation_ratio >= 0.5
    
    def _dictionary_translate(self, text, rules):
        """Attempt intelligent translation using dictionary rules with phrase matching"""
        # First try to find exact phrase match
        exact_match = rules.filter(source_text__iexact=text).first()
        if exact_match:
            return exact_match.target_text

        # Try to find partial phrase matches (longer phrases first)
        words = text.split()

        # Try different phrase lengths (from longest to shortest)
        for phrase_length in range(len(words), 0, -1):
            for i in range(len(words) - phrase_length + 1):
                phrase = " ".join(words[i:i + phrase_length])
                phrase_match = rules.filter(source_text__iexact=phrase).first()

                if phrase_match:
                    # Found a phrase match, use it as base and continue with remaining words
                    remaining_before = words[:i]
                    remaining_after = words[i + phrase_length:]

                    translated_parts = []

                    # Translate words before the matched phrase
                    if remaining_before:
                        before_translation = self._translate_word_list(remaining_before, rules)
                        translated_parts.extend(before_translation)

                    # Add the matched phrase translation
                    translated_parts.append(phrase_match.target_text)

                    # Translate words after the matched phrase
                    if remaining_after:
                        after_translation = self._translate_word_list(remaining_after, rules)
                        translated_parts.extend(after_translation)

                    return " ".join(translated_parts)

        # If no phrase matches found, fall back to word-by-word with smart handling
        return self._smart_word_translation(words, rules)

    def _translate_word_list(self, words, rules):
        """Translate a list of words with context awareness"""
        translated = []
        for word in words:
            # Clean word (remove punctuation for matching)
            clean_word = word.strip('.,!?;:')
            punctuation = word[len(clean_word):] if len(word) > len(clean_word) else ""

            rule_match = rules.filter(source_text__iexact=clean_word).first()
            if rule_match:
                translated.append(rule_match.target_text + punctuation)
            else:
                translated.append(word)  # Keep original if no match
        return translated

    def _smart_word_translation(self, words, rules):
        """Smart word-by-word translation with grammatical awareness"""
        translated_words = []
        original_count = 0

        for word in words:
            # Clean word (remove punctuation for matching)
            clean_word = word.strip('.,!?;:')
            punctuation = word[len(clean_word):] if len(word) > len(clean_word) else ""

            # Look for exact match in rules
            rule_match = rules.filter(source_text__iexact=clean_word).first()
            if rule_match:
                translated_words.append(rule_match.target_text + punctuation)
            else:
                # Try case-insensitive match
                case_match = rules.filter(source_text__icontains=clean_word).first()
                if case_match:
                    translated_words.append(case_match.target_text + punctuation)
                else:
                    # Keep original word
                    translated_words.append(word)
                    original_count += 1

        # Always return the translation, even if partial
        # The _is_good_translation method will decide if it's good enough
        return " ".join(translated_words)

    def _get_relevant_context(self, text, source_lang, target_lang, limit=50):
        """Get most relevant translation rules for the given text"""
        # Get all rules for the source language
        all_rules = TranslationRule.objects.filter(source_language=source_lang)

        # Split input text into words for matching
        text_words = text.lower().split()

        # Score rules based on word overlap
        scored_rules = []
        for rule in all_rules:
            rule_words = rule.source_text.lower().split()

            # Calculate overlap score
            overlap = len(set(text_words) & set(rule_words))
            if overlap > 0:
                # Boost score for exact matches
                if rule.source_text.lower() == text.lower():
                    overlap += 10
                # Boost score for partial matches
                elif any(word in rule.source_text.lower() for word in text_words):
                    overlap += 2

                scored_rules.append((overlap, rule))

        # Sort by score (descending) and return top rules
        scored_rules.sort(key=lambda x: x[0], reverse=True)
        return [rule for _, rule in scored_rules[:limit]]

    def _get_linguistic_context(self, source_lang, target_lang):
        """Get linguistic context based on the languages involved"""
        context = ""

        if 'teduray' in [source_lang, target_lang]:
            context += """
TEDURAY LINGUISTIC FEATURES:
- Uses visibility markers: -e (visible/tanaw) vs -o (non-visible/di-tanaw)
- Ergative-absolutive alignment: different treatment for subjects and objects
- Person marking: 1st (begen/ku), 2nd (beem/em), 3rd (been/nu)
- Number: singular vs plural with specific markers
- Demonstratives: eni (this/proximal), enan (that/medial), eno (that/distal)
- Word order: Verb-Subject-Object with complex affixation
- Determiners: ike/iko (core), ide/ido (plural), be (extended)
"""

        if 'tagalog' in [source_lang, target_lang]:
            context += """
TAGALOG LINGUISTIC FEATURES:
- Uses focus system with different verb forms
- Markers: ang (nominative), ng (genitive), sa (dative/locative)
- Pronouns: ako/ko (1st), ikaw/mo (2nd), siya/niya (3rd)
- Demonstratives: ito (this), iyan (that-near you), iyon (that-far)
- Word order: Verb-Subject-Object or Predicate-Subject
- Aspect markers: mag- (actor focus), -in (object focus), i- (benefactive)
"""

        if 'english' in [source_lang, target_lang]:
            context += """
ENGLISH LINGUISTIC FEATURES:
- Subject-Verb-Object word order
- Articles: a/an (indefinite), the (definite)
- Pronouns: I/me, you, he/him, she/her, it, we/us, they/them
- Demonstratives: this/these (near), that/those (far)
- Tense markers: -ed (past), will (future), -ing (progressive)
- No complex case marking system
"""

        return context.strip()

    def _enhanced_api_translate(self, text, source_lang, target_lang, rules):
        """Enhanced Gemini translation with translate.py data and real web search"""
        # Analyze punctuation and sentence type
        punctuation_info = self._analyze_punctuation(text)

        # Get grammatical analysis
        grammatical_analysis = self._analyze_grammar(text, source_lang)

        # STEP 1: Search translate.py data directly
        translate_py_matches = self._search_translate_py_data(text, source_lang, target_lang)

        # STEP 2: Get database examples for additional context
        pattern_examples = self._get_pattern_examples(text, source_lang, target_lang, grammatical_analysis)
        general_examples = TranslationRule.objects.filter(
            source_language=source_lang
        ).order_by('?')[:10]

        # STEP 3: Perform real web search for additional context
        web_search_results = []
        try:
            search_queries = [
                f'"{text}" {source_lang} to {target_lang} translation',
                f'Teduray language "{text}"',
                f'{source_lang} "{text}" meaning'
            ]

            for query in search_queries[:2]:  # Limit to 2 searches
                results = self._real_web_search(query, max_results=3)
                web_search_results.extend(results)
                if len(web_search_results) >= 5:  # Limit total results
                    break
        except Exception as e:
            print(f"Web search error: {e}")

        # Build comprehensive context
        translate_py_context = ""
        if translate_py_matches:
            translate_py_context = "TRANSLATE.PY MATCHES:\n" + "\n".join([
                f"'{match['source']}' → '{match['target']}' ({match['match_type']})"
                for match in translate_py_matches[:10]
            ])

        # Database context
        all_examples = list(pattern_examples) + list(general_examples)
        seen = set()
        unique_examples = []
        for rule in all_examples:
            key = (rule.source_text, rule.target_text)
            if key not in seen:
                seen.add(key)
                unique_examples.append(rule)

        database_context = "DATABASE EXAMPLES:\n" + "\n".join([
            f"'{rule.source_text}' → '{rule.target_text}'"
            for rule in unique_examples[:15]
        ])

        # Web search context
        web_context = ""
        if web_search_results:
            web_context = "WEB SEARCH RESULTS:\n" + "\n".join([
                f"• {result['title']}: {result['snippet'][:100]}..."
                for result in web_search_results[:3]
            ])

        # Get linguistic rules and patterns
        linguistic_rules = self._get_linguistic_rules(source_lang, target_lang)

        # Create enhanced prompt with all available data sources
        prompt = f"""You are an expert linguist with access to comprehensive translation resources for {source_lang.title()} and {target_lang.title()}.

CRITICAL INSTRUCTIONS:
1. PRIORITIZE translate.py data matches (most authoritative)
2. Use web search results for additional context
3. Apply database examples for grammatical patterns
4. PRESERVE original punctuation and sentence type: {punctuation_info}
5. Focus on GRAMMATICAL STRUCTURE, not word-by-word translation

PUNCTUATION ANALYSIS: {punctuation_info}
GRAMMATICAL ANALYSIS: {grammatical_analysis}

{translate_py_context}

{database_context}

{web_context}

SPECIFIC WORD TRANSLATIONS (CRITICAL - MUST USE THESE):
• kumain → mënama (eat) - NEVER use 'mama' alone
• lalaki → lagëy (man) - NEVER keep as 'lalaki'
• matanda → lukës (old) - NEVER use 'mëlëmëndëg'
• tinapay → ëfan (bread)

GRAMMATICAL PATTERN FOR "kumain ang matandang lalaki ng tinapay":
CORRECT: "Mënama i lukëse lagëy ëfan"
Pattern: VERB + i + ADJECTIVE + NOUN + OBJECT

LINGUISTIC RULES FOR {source_lang.title()} → {target_lang.title()}:
{linguistic_rules}

INPUT TO TRANSLATE: "{text}"

TRANSLATION APPROACH:
1. CHECK translate.py matches first - use exact matches if available
2. APPLY web search context for unfamiliar terms
3. USE specific word translations from the critical list above
4. FOLLOW grammatical pattern: VERB + i + ADJECTIVE + NOUN + OBJECT
5. PRESERVE original punctuation (?, !, ., etc.)
6. CREATE natural {target_lang} expression

EXAMPLES OF CORRECT TRANSLATIONS:
- "kumain ang matandang lalaki ng tinapay" → "Mënama i lukëse lagëy ëfan"
- "Kumusta ka?" → "Faanuy kaame?" (question mark preserved)
- "Maganda!" → "Fiyoy!" (exclamation preserved)

PROVIDE ONLY THE GRAMMATICALLY CORRECT TRANSLATION WITH PROPER PUNCTUATION:"""

        # Try with retries and better error handling
        for attempt in range(self.max_retries):
            try:
                response = self.model.generate_content(prompt)
                if response and response.text:
                    # Clean up the response but preserve punctuation
                    result = response.text.strip()
                    # Remove quotes but keep punctuation
                    if result.startswith('"') and result.endswith('"'):
                        result = result[1:-1]
                    if result.startswith("'") and result.endswith("'"):
                        result = result[1:-1]
                    return result
                else:
                    raise Exception("Empty response from Gemini")

            except Exception as e:
                error_msg = str(e).lower()
                if ("quota" in error_msg or "rate" in error_msg) and attempt < self.max_retries - 1:
                    print(f"Rate limit hit, waiting {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                    continue
                elif attempt < self.max_retries - 1:
                    print(f"API error: {e}, retrying...")
                    time.sleep(1)
                    continue
                else:
                    print(f"Enhanced Gemini API failed after {self.max_retries} attempts: {e}")
                    raise e

    def _analyze_punctuation(self, text):
        """Analyze punctuation and sentence type"""
        if text.endswith('?'):
            return "Interrogative sentence (question) - MUST preserve question mark (?)"
        elif text.endswith('!'):
            return "Exclamatory sentence - MUST preserve exclamation mark (!)"
        elif text.endswith('.'):
            return "Declarative sentence - MUST preserve period (.)"
        else:
            return "Simple statement - add appropriate punctuation if needed"

    def _analyze_grammar(self, text, source_lang):
        """Stub for grammatical analysis. Returns a simple string for now."""
        # In a real implementation, this would use NLP or rules for Tagalog/Teduray/English
        # For now, just return a placeholder
        return f"Basic grammatical analysis for '{text}' in {source_lang}."

    def _api_translate(self, text, source_lang, target_lang, rules):
        """Intelligent translation using Gemini with grammatical rule learning"""
        # First, try to understand the grammatical structure
        grammatical_analysis = self._analyze_grammar(text, source_lang)

        # Get relevant examples that match the grammatical pattern
        pattern_examples = self._get_pattern_examples(text, source_lang, target_lang, grammatical_analysis)

        # Get general examples for context
        general_examples = TranslationRule.objects.filter(
            source_language=source_lang
        ).order_by('?')[:15]

        # Combine all examples
        all_examples = list(pattern_examples) + list(general_examples)

        # Remove duplicates
        seen = set()
        unique_examples = []
        for rule in all_examples:
            key = (rule.source_text, rule.target_text)
            if key not in seen:
                seen.add(key)
                unique_examples.append(rule)

        # Build context with grammatical patterns
        examples_context = "\n".join([
            f"'{rule.source_text}' → '{rule.target_text}'"
            for rule in unique_examples[:30]
        ])

        # Get linguistic rules and patterns
        linguistic_rules = self._get_linguistic_rules(source_lang, target_lang)

        # Create an intelligent prompt that focuses on grammatical understanding
        prompt = f"""You are an expert linguist specializing in {source_lang.title()} and {target_lang.title()} grammar and translation.

CRITICAL INSTRUCTION: Focus on GRAMMATICAL STRUCTURE and MEANING, not word-by-word translation.

GRAMMATICAL ANALYSIS OF INPUT:
{grammatical_analysis}

LINGUISTIC RULES FOR {source_lang.title()} → {target_lang.title()}:
{linguistic_rules}

REFERENCE TRANSLATIONS FROM DATABASE:
{examples_context}

INPUT TO TRANSLATE: "{text}"

TRANSLATION APPROACH:
1. ANALYZE the grammatical structure of the {source_lang} sentence
2. UNDERSTAND the complete meaning and intent
3. APPLY {target_lang} grammatical patterns (not word-by-word)
4. USE database examples as reference for vocabulary
5. CREATE natural, grammatically correct {target_lang} expression

FOR TAGALOG → TEDURAY SPECIFICALLY:
- Understand focus markers (ang/ng/sa) and convert to Teduray equivalents
- Recognize verb aspects and tense
- Handle pronouns and possessives properly
- Maintain natural Teduray word order and expression
- Use appropriate Teduray grammatical markers

EXAMPLES OF GRAMMATICAL TRANSLATION (NOT WORD-BY-WORD):
- "Gusto kong kumain" → "M�uyotu mama" (not "gusto ku kumain")
- "Maganda ang bahay" → "Fiyoy ike lawi" (not "maganda ang bahay")
- "Mahal kita" → "M�uyotu beem" (not "mahal ka")

PROVIDE ONLY THE GRAMMATICALLY CORRECT TRANSLATION:"""

        # Try with retries and better error handling
        for attempt in range(self.max_retries):
            try:
                response = self.model.generate_content(prompt)
                if response and response.text:
                    # Clean up the response
                    result = response.text.strip()
                    # Remove any quotes or extra formatting
                    if result.startswith('"') and result.endswith('"'):
                        result = result[1:-1]
                    if result.startswith("'") and result.endswith("'"):
                        result = result[1:-1]
                    return result
                else:
                    raise Exception("Empty response from Gemini")

            except Exception as e:
                error_msg = str(e).lower()
                if ("quota" in error_msg or "rate" in error_msg) and attempt < self.max_retries - 1:
                    # Rate limit - wait and retry
                    print(f"Rate limit hit, waiting {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                    continue
                elif attempt < self.max_retries - 1:
                    # Other error - shorter wait and retry
                    print(f"API error: {e}, retrying...")
                    time.sleep(1)
                    continue
                else:
                    # Final attempt failed
                    print(f"Gemini API failed after {self.max_retries} attempts: {e}")
                    raise e

    def _search_translate_py_data(self, text, source_lang, target_lang):
        """Stub for searching translate.py data. Returns a list of matches if any."""
        # For now, just do a simple substring match in the loaded data
        matches = []
        for entry in getattr(self, 'translate_py_data', []):
            if text.strip().lower() in entry['source'].strip().lower():
                matches.append(entry)
        return matches

    def _web_fallback_translate(self, text, source_lang, target_lang):
        """Try to find a translation from other online resources if Bible.com or local sources fail."""
        try:
            try:
                import bs4
                from bs4 import BeautifulSoup
            except ImportError:
                print("BeautifulSoup4 (bs4) is not installed. Please install it with 'pip install beautifulsoup4'.")
                return None
            query = f"{text} Tagalog Teduray Bible verse"
            import requests
            search_url = f"https://www.google.com/search?q={requests.utils.quote(query)}"
            headers = {'User-Agent': 'Mozilla/5.0'}
            resp = requests.get(search_url, headers=headers, timeout=10)
            if resp.status_code == 200:
                soup = BeautifulSoup(resp.text, 'html.parser')
                for div in soup.find_all('div', class_='BNeawe s3v9rd AP7Wnd'):
                    snippet = div.get_text()
                    if 'Teduray' in snippet and 'Tagalog' in snippet:
                        return snippet
                    if len(snippet.split()) > 5:
                        return snippet
            return None
        except Exception as e:
            print(f"Web fallback search failed: {e}")
            return None

    def _load_translate_py_data(self):
        """Load translation data from translate.py as a list of dicts: [{'source': ..., 'target': ..., 'match_type': ...}, ...]"""
        data = []
        try:
            import csv
            import codecs
            # Try to read translate.py as a tab-separated file, skipping header lines
            path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'translate.py')
            with codecs.open(path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            for line in lines:
                if line.strip().startswith('#') or not line.strip():
                    continue
                # Heuristic: expect tab-separated lines with at least 3 columns
                parts = line.strip().split('\t')
                if len(parts) == 3:
                    tagalog, english, teduray = parts
                    data.append({'source': tagalog, 'target': teduray, 'match_type': 'translate.py'})
            print(f"Successfully read translate.py with utf-8 encoding\nLoaded {len(data)} translations from translate.py")
        except Exception as e:
            print(f"Failed to load translate.py: {e}")
        return data

    def _fetch_bible_verse(self, book, chapter, verse, lang_code):
        """Fetch a Bible verse from Bible.com for a given language code, supporting parallel Teduray (TIY) and Tagalog (TLAB)."""
        try:
            version_map = {
                'tagalog': 'TLAB',  # Tagalog Ang Biblia
                'teduray': 'TIY', # Teduray (TIY)
                'english': 'KJV', # King James Version
            }
            version = version_map.get(lang_code.lower(), 'TLAB')
            url = f"https://www.bible.com/bible/1266/{book.upper()}.{chapter}.{version}"
            headers = {'User-Agent': 'Mozilla/5.0'}
            resp = requests.get(url, headers=headers, timeout=10)
            if resp.status_code != 200:
                return None
            # Extract the verse using the current Bible.com HTML structure
            verse_pattern = re.compile(rf'<span[^>]+class="verse"[^>]+data-usfm="{book.upper()}\\.{chapter}\\.{verse}"[^>]*>.*?</span>\\s*<span[^>]+class="content"[^>]*>(.*?)</span>', re.DOTALL)
            match = verse_pattern.search(resp.text)
            if match:
                verse_html = match.group(1)
                verse_text = re.sub(r'<[^>]+>', '', verse_html)
                return verse_text.strip()
            # Fallback: try to extract verse from meta tag or other patterns
            match2 = re.search(r'<meta name="description" content="([^"]+)"', resp.text)
            if match2:
                return match2.group(1).strip()
            return None
        except Exception as e:
            print(f"Bible.com fetch failed: {e}")
            return None

    def _get_pattern_examples(self, text, source_lang, target_lang, grammatical_analysis):
        """Stub for getting pattern examples. Returns an empty list for now."""
        # In a real implementation, this would return relevant TranslationRule objects
        return []

    def _real_web_search(self, query, max_results=3):
        """Perform a real web search using SerpAPI and return a list of dicts with 'title' and 'snippet'."""
        try:
            import requests
            serpapi_key = "df39f706f7af4b3ac610d27b3ff8c23a1e3aa8765f574f59eece16cf86a41cdf"
            params = {
                "q": query,
                "api_key": serpapi_key,
                "engine": "google",
                "num": max_results,
                "hl": "en",
            }
            url = "https://serpapi.com/search"
            resp = requests.get(url, params=params, timeout=10)
            if resp.status_code != 200:
                print(f"SerpAPI error: status {resp.status_code}")
                return []
            data = resp.json()
            results = []
            # Prefer organic_results, fallback to other fields if needed
            for item in data.get("organic_results", [])[:max_results]:
                title = item.get("title", "")
                snippet = item.get("snippet", "")
                if title or snippet:
                    results.append({"title": title, "snippet": snippet})
            # Fallback: try answer_box or other fields if no organic results
            if not results and "answer_box" in data:
                ab = data["answer_box"]
                title = ab.get("title", "")
                snippet = ab.get("snippet", "") or ab.get("answer", "")
                if title or snippet:
                    results.append({"title": title, "snippet": snippet})
            return results
        except Exception as e:
            print(f"SerpAPI web search failed: {e}")
            return []

    def _get_linguistic_rules(self, source_lang, target_lang):
        """Stub for linguistic rules. Returns an empty string for now."""
        # In a real implementation, this would return language-specific rules
        return ""

    def _multi_step_advanced_translation(self, text, source_lang, target_lang, rules):
        """
        Advanced multi-step translation pipeline as per ENHANCED_TRANSLATION_METHODS.md:
        1. Grammatical and semantic analysis of input (Tagalog/Teduray/English)
        2. Phrase/word segmentation and lookup (translate.py, database)
        3. Syntactic reordering and marker adjustment (focus, aspect, visibility, etc.)
        4. Cultural/idiomatic refinement (avoid literalism, use natural Teduray)
        5. Validation: ensure output is idiomatic, grammatical, and culturally appropriate
        6. Final Gemini pass for naturalness (with strict prompt to avoid meta-output)
        """
        # Step 1: Grammatical and semantic analysis
        grammatical_analysis = self._analyze_grammar(text, source_lang)
        # Step 2: Phrase/word segmentation and lookup
        py_match = self._find_translate_py_match(text, source_lang.lower(), target_lang.lower())
        if py_match:
            return self._preserve_punctuation(text, py_match)
        db_match = self._find_flexible_match(text, source_lang)
        if db_match:
            return self._preserve_punctuation(text, db_match.target_text)
        # Step 3: Syntactic reordering and marker adjustment (stub: could use rules)
        # For now, just pass through to Gemini with explicit instructions
        # Step 4: Cultural/idiomatic refinement and validation
        # Step 5: Final Gemini pass for naturalness
        linguistic_rules = self._get_linguistic_rules(source_lang, target_lang)
        prompt = f"""You are a professional Teduray translator. Translate the following {source_lang} text to natural, idiomatic, and grammatically correct {target_lang}.\n\n- DO NOT explain, comment, or output anything except the translation.\n- Use Teduray Bible style and cultural norms if relevant.\n- Avoid literal word-for-word translation.\n- Use correct Teduray focus, aspect, and visibility markers.\n- If the input is a Bible verse, match Teduray Bible style.\n- If the input is a phrase or sentence, use natural Teduray.\n- Preserve original punctuation.\n- Output ONLY the translation, nothing else.\n\nLINGUISTIC RULES:\n{linguistic_rules}\n\nGRAMMATICAL ANALYSIS:\n{grammatical_analysis}\n\nINPUT:\n{text}\n\nTRANSLATION:"""
        for attempt in range(self.max_retries):
            try:
                response = self.model.generate_content(prompt)
                if response and response.text:
                    result = response.text.strip()
                    # Remove quotes and meta-output
                    if result.startswith('"') and result.endswith('"'):
                        result = result[1:-1]
                    if result.startswith("'") and result.endswith("'"):
                        result = result[1:-1]
                    # Remove meta-linguistic output if present
                    result = re.sub(r'^(Translation:|Teduray:|Output:)', '', result, flags=re.I).strip()
                    return self._preserve_punctuation(text, result)
                else:
                    raise Exception("Empty response from Gemini")
            except Exception as e:
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    print(f"Multi-step Gemini translation failed: {e}")
                    raise e

