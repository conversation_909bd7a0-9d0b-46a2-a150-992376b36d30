import google.generativeai as genai
import time
import requests
import os
import re
import csv
from django.conf import settings
from .models import TranslationRule

class TranslationService:
    def __init__(self):
        genai.configure(api_key=settings.GEMINI_API_KEY)
        self.model = genai.GenerativeModel('models/gemini-1.5-flash')
        self.max_retries = 3
        self.retry_delay = 5  # seconds
        self.translate_py_data = self._load_translate_py_data()
        self.translate_py_corpus = self._load_translate_py_corpus()
        self._bible_cache = {}
        # Load Teduray Bible corpus from CSV
        self._teduray_bible_corpus = self._load_teduray_bible_corpus()

    def _load_teduray_bible_corpus(self):
        """Load Teduray Bible verses from CSV into a dict: {(book, chapter, verse): text}. Skips invalid rows. Accepts 'text' or 'teduray_text' as the verse column."""
        corpus = {}
        csv_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'teduray_bible_verses.csv')
        if not os.path.exists(csv_path):
            print(f"Teduray Bible CSV not found at {csv_path}")
            return corpus
        try:
            with open(csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for i, row in enumerate(reader, 2):  # start at 2 for header
                    try:
                        book = row['book'].strip().upper()
                        chapter = int(row['chapter'])
                        verse = int(row['verse'])
                        # Accept either 'text' or 'teduray_text' as the verse column
                        text = row.get('text') or row.get('teduray_text')
                        if text is not None:
                            text = text.strip()
                        if not (book and chapter and verse and text):
                            raise ValueError('Missing value')
                        key = (book, chapter, verse)
                        corpus[key] = text
                    except Exception as e:
                        print(f"Skipping invalid row {i} in Teduray Bible CSV: {e} | Row: {row}")
            print(f"Loaded {len(corpus)} Teduray Bible verses from CSV.")
        except Exception as e:
            print(f"Failed to load Teduray Bible corpus: {e}")
        return corpus

    def _lookup_teduray_bible_corpus(self, book, chapter, verse):
        """Return Teduray verse from local corpus if available."""
        key = (book.strip().upper(), int(chapter), int(verse))
        return self._teduray_bible_corpus.get(key)

    def _load_translate_py_corpus(self):
        # Load Tagalog↔Teduray pairs from translate.py (tab-separated, skip header lines)
        corpus = []
        try:
            with open(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'translate.py'), encoding='utf-8') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '\t' in line:
                        parts = line.strip().split('\t')
                        if len(parts) == 3:
                            tagalog, _, teduray = parts
                            corpus.append({'tagalog': tagalog.strip(), 'teduray': teduray.strip()})
        except Exception as e:
            print(f"Error loading translate.py corpus: {e}")
        return corpus

    def _find_translate_py_match(self, text, source_lang, target_lang):
        # Direct match in translate.py corpus (case-insensitive, flexible punctuation)
        norm = lambda s: re.sub(r'[^\w\s]', '', s.lower())
        text_norm = norm(text)
        for entry in self.translate_py_corpus:
            src = entry.get(source_lang, '').strip()
            tgt = entry.get(target_lang, '').strip()
            if norm(src) == text_norm:
                return tgt
        return None

    def translate(self, text, source_lang, target_lang):
        """Main translation method with advanced multi-step pipeline."""
        # Step 1: Official Teduray Bible verse match (return immediately if found)
        # Try to detect if input is a Bible verse reference and look up in Teduray Bible corpus
        # (You may want to improve this detection logic as needed)
        bible_match = None
        # Example: try to parse as (book, chapter, verse) or use your own detection logic
        # For now, just check if the text matches a known verse in the corpus
        for (book, chapter, verse), teduray_text in self._teduray_bible_corpus.items():
            if text.strip().lower() == teduray_text.strip().lower():
                bible_match = teduray_text
                break
        if bible_match:
            return bible_match

        # Step 2: Database exact match (do not return yet)
        exact_match = self._find_flexible_match(text, source_lang)
        # Step 3: translate.py direct match (do not return yet)
        py_match = self._find_translate_py_match(text, source_lang.lower(), target_lang.lower())
        # Step 4: Enhanced single word/phrase translation (dictionary)
        all_rules = TranslationRule.objects.filter(source_language=source_lang)
        dictionary_result = None
        if len(text.split()) <= 3:
            dictionary_result = self._dictionary_translate(text, all_rules)
            if dictionary_result and not self._is_good_translation(text, dictionary_result):
                dictionary_result = None

        # Step 5: Advanced comprehensive translation (multi-step, always use Gemini for non-Bible cases)
        # Use the best available match as input to Gemini, or the original text if no match
        base_translation = py_match or (exact_match.target_text if exact_match else None) or dictionary_result or text
        try:
            advanced_result = self._multi_step_advanced_translation(base_translation, source_lang, target_lang, all_rules)
            if advanced_result:
                return advanced_result
        except Exception as e:
            print(f"Advanced multi-step translation failed: {e}")
        # Step 6: Gemini fallback (should rarely be needed)
        try:
            api_result = self._enhanced_api_translate(text, source_lang, target_lang, all_rules)
            if api_result and not api_result.startswith("[Translation limited]"):
                return api_result
        except Exception as e:
            print(f"Gemini API translation failed: {e}")
        # Step 7: Web search fallback
        try:
            web_result = self._web_fallback_translate(text, source_lang, target_lang)
            if web_result:
                return web_result
        except Exception as e:
            print(f"Web translation failed: {e}")
        # Step 8: Last resort
        return f"[No translation available] {text}"

    def _find_flexible_match(self, text, source_lang):
        """Find database match with flexible punctuation handling"""
        # First try exact match
        exact_match = TranslationRule.objects.filter(
            source_language=source_lang,
            source_text__iexact=text
        ).first()

        if exact_match:
            return exact_match

        # Try with normalized text (remove common punctuation)
        normalized_text = text.strip().rstrip('.,!?;:')
        if normalized_text != text:
            normalized_match = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__iexact=normalized_text
            ).first()

            if normalized_match:
                return normalized_match

        # Try adding common punctuation if input doesn't have it
        if not text.endswith('.'):
            with_period = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__iexact=text + '.'
            ).first()

            if with_period:
                return with_period

        # Try removing punctuation from database entries and comparing
        # This handles cases where database has punctuation but input doesn't
        potential_matches = TranslationRule.objects.filter(
            source_language=source_lang,
            source_text__icontains=normalized_text[:20] if len(normalized_text) > 20 else normalized_text
        )

        for rule in potential_matches:
            rule_normalized = rule.source_text.strip().rstrip('.,!?;:')
            if rule_normalized.lower() == normalized_text.lower():
                return rule

        return None

    def _detect_unfamiliar_words(self, text, all_rules):
        """Detect words that are not in our database and might need web search"""
        # Extract words from text (remove punctuation)
        import re
        words = re.findall(r'\b\w+\b', text.lower())

        # Get all known words from database
        known_words = set()
        for rule in all_rules:
            rule_words = re.findall(r'\b\w+\b', rule.source_text.lower())
            known_words.update(rule_words)

        # Find unfamiliar words (not in database)
        unfamiliar = []
        for word in words:
            # Skip very common words that don't need translation
            common_words = {'ang', 'ng', 'sa', 'na', 'at', 'ay', 'ko', 'mo', 'ka', 'ako', 'ikaw', 'siya',
                        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
                        'i', 'you', 'he', 'she', 'it', 'we', 'they', 'is', 'are', 'was', 'were', 'be', 'been'}

            if word not in common_words and word not in known_words and len(word) > 2:
                unfamiliar.append(word)

        # Return unfamiliar words if they make up significant portion of text
        if len(unfamiliar) > 0 and len(unfamiliar) / len(words) > 0.3:
            return unfamiliar

        return []

    def _preserve_punctuation(self, original_text, translated_text):
        """Preserve punctuation from original text in translation"""
        # Get the punctuation from the original text
        if original_text.endswith('?'):
            if not translated_text.endswith('?'):
                return translated_text.rstrip('.,!') + '?'
        elif original_text.endswith('!'):
            if not translated_text.endswith('!'):
                return translated_text.rstrip('.,?') + '!'
        elif original_text.endswith('.'):
            if not translated_text.endswith('.'):
                return translated_text.rstrip('?!') + '.'

        return translated_text

    def _is_good_translation(self, original, translated):
        """Check if dictionary translation is good enough"""
        if not translated:
            return False

        # Count how many words were actually translated vs kept original
        original_words = set(original.lower().split())
        translated_words = set(translated.lower().split())

        # If more than 50% of words were translated, consider it good
        overlap = len(original_words & translated_words)
        translation_ratio = 1 - (overlap / len(original_words)) if original_words else 0

        return translation_ratio >= 0.5
    
    def _dictionary_translate(self, text, rules):
        """Attempt intelligent translation using dictionary rules with phrase matching"""
        # First try to find exact phrase match
        exact_match = rules.filter(source_text__iexact=text).first()
        if exact_match:
            return exact_match.target_text

        # Try to find partial phrase matches (longer phrases first)
        words = text.split()

        # Try different phrase lengths (from longest to shortest)
        for phrase_length in range(len(words), 0, -1):
            for i in range(len(words) - phrase_length + 1):
                phrase = " ".join(words[i:i + phrase_length])
                phrase_match = rules.filter(source_text__iexact=phrase).first()

                if phrase_match:
                    # Found a phrase match, use it as base and continue with remaining words
                    remaining_before = words[:i]
                    remaining_after = words[i + phrase_length:]

                    translated_parts = []

                    # Translate words before the matched phrase
                    if remaining_before:
                        before_translation = self._translate_word_list(remaining_before, rules)
                        translated_parts.extend(before_translation)

                    # Add the matched phrase translation
                    translated_parts.append(phrase_match.target_text)

                    # Translate words after the matched phrase
                    if remaining_after:
                        after_translation = self._translate_word_list(remaining_after, rules)
                        translated_parts.extend(after_translation)

                    return " ".join(translated_parts)

        # If no phrase matches found, fall back to word-by-word with smart handling
        return self._smart_word_translation(words, rules)

    def _translate_word_list(self, words, rules):
        """Translate a list of words with context awareness"""
        translated = []
        for word in words:
            # Clean word (remove punctuation for matching)
            clean_word = word.strip('.,!?;:')
            punctuation = word[len(clean_word):] if len(word) > len(clean_word) else ""

            rule_match = rules.filter(source_text__iexact=clean_word).first()
            if rule_match:
                translated.append(rule_match.target_text + punctuation)
            else:
                translated.append(word)  # Keep original if no match
        return translated

    def _smart_word_translation(self, words, rules):
        """Smart word-by-word translation with grammatical awareness"""
        translated_words = []
        original_count = 0

        for word in words:
            # Clean word (remove punctuation for matching)
            clean_word = word.strip('.,!?;:')
            punctuation = word[len(clean_word):] if len(word) > len(clean_word) else ""

            # Look for exact match in rules
            rule_match = rules.filter(source_text__iexact=clean_word).first()
            if rule_match:
                translated_words.append(rule_match.target_text + punctuation)
            else:
                # Try case-insensitive match
                case_match = rules.filter(source_text__icontains=clean_word).first()
                if case_match:
                    translated_words.append(case_match.target_text + punctuation)
                else:
                    # Keep original word
                    translated_words.append(word)
                    original_count += 1

        # Always return the translation, even if partial
        # The _is_good_translation method will decide if it's good enough
        return " ".join(translated_words)

    def _get_relevant_context(self, text, source_lang, target_lang, limit=50):
        """Get most relevant translation rules for the given text"""
        # Get all rules for the source language
        all_rules = TranslationRule.objects.filter(source_language=source_lang)

        # Split input text into words for matching
        text_words = text.lower().split()

        # Score rules based on word overlap
        scored_rules = []
        for rule in all_rules:
            rule_words = rule.source_text.lower().split()

            # Calculate overlap score
            overlap = len(set(text_words) & set(rule_words))
            if overlap > 0:
                # Boost score for exact matches
                if rule.source_text.lower() == text.lower():
                    overlap += 10
                # Boost score for partial matches
                elif any(word in rule.source_text.lower() for word in text_words):
                    overlap += 2

                scored_rules.append((overlap, rule))

        # Sort by score (descending) and return top rules
        scored_rules.sort(key=lambda x: x[0], reverse=True)
        return [rule for _, rule in scored_rules[:limit]]

    def _get_linguistic_context(self, source_lang, target_lang):
        """Get linguistic context based on the languages involved"""
        context = ""

        if 'teduray' in [source_lang, target_lang]:
            context += """
TEDURAY LINGUISTIC FEATURES:
- Uses visibility markers: -e (visible/tanaw) vs -o (non-visible/di-tanaw)
- Ergative-absolutive alignment: different treatment for subjects and objects
- Person marking: 1st (begen/ku), 2nd (beem/em), 3rd (been/nu)
- Number: singular vs plural with specific markers
- Demonstratives: eni (this/proximal), enan (that/medial), eno (that/distal)
- Word order: Verb-Subject-Object with complex affixation
- Determiners: ike/iko (core), ide/ido (plural), be (extended)
"""

        if 'tagalog' in [source_lang, target_lang]:
            context += """
TAGALOG LINGUISTIC FEATURES:
- Uses focus system with different verb forms
- Markers: ang (nominative), ng (genitive), sa (dative/locative)
- Pronouns: ako/ko (1st), ikaw/mo (2nd), siya/niya (3rd)
- Demonstratives: ito (this), iyan (that-near you), iyon (that-far)
- Word order: Verb-Subject-Object or Predicate-Subject
- Aspect markers: mag- (actor focus), -in (object focus), i- (benefactive)
"""

        if 'english' in [source_lang, target_lang]:
            context += """
ENGLISH LINGUISTIC FEATURES:
- Subject-Verb-Object word order
- Articles: a/an (indefinite), the (definite)
- Pronouns: I/me, you, he/him, she/her, it, we/us, they/them
- Demonstratives: this/these (near), that/those (far)
- Tense markers: -ed (past), will (future), -ing (progressive)
- No complex case marking system
"""

        return context.strip()

    def _enhanced_api_translate(self, text, source_lang, target_lang, rules):
        """Enhanced Gemini translation with translate.py data and real web search"""
        # Analyze punctuation and sentence type
        punctuation_info = self._analyze_punctuation(text)

        # Get grammatical analysis
        grammatical_analysis = self._analyze_grammar(text, source_lang)

        # STEP 1: Search translate.py data directly
        translate_py_matches = self._search_translate_py_data(text, source_lang, target_lang)

        # STEP 2: Get database examples for additional context
        pattern_examples = self._get_pattern_examples(text, source_lang, target_lang, grammatical_analysis)
        general_examples = TranslationRule.objects.filter(
            source_language=source_lang
        ).order_by('?')[:10]

        # STEP 3: Perform real web search for additional context
        web_search_results = []
        try:
            search_queries = [
                f'"{text}" {source_lang} to {target_lang} translation',
                f'Teduray language "{text}"',
                f'{source_lang} "{text}" meaning'
            ]

            for query in search_queries[:2]:  # Limit to 2 searches
                results = self._real_web_search(query, max_results=3)
                web_search_results.extend(results)
                if len(web_search_results) >= 5:  # Limit total results
                    break
        except Exception as e:
            print(f"Web search error: {e}")

        # Build comprehensive context
        translate_py_context = ""
        if translate_py_matches:
            translate_py_context = "TRANSLATE.PY MATCHES:\n" + "\n".join([
                f"'{match['source']}' → '{match['target']}' ({match['match_type']})"
                for match in translate_py_matches[:10]
            ])

        # Database context
        all_examples = list(pattern_examples) + list(general_examples)
        seen = set()
        unique_examples = []
        for rule in all_examples:
            key = (rule.source_text, rule.target_text)
            if key not in seen:
                seen.add(key)
                unique_examples.append(rule)

        database_context = "DATABASE EXAMPLES:\n" + "\n".join([
            f"'{rule.source_text}' → '{rule.target_text}'"
            for rule in unique_examples[:15]
        ])

        # Web search context
        web_context = ""
        if web_search_results:
            web_context = "WEB SEARCH RESULTS:\n" + "\n".join([
                f"• {result['title']}: {result['snippet'][:100]}..."
                for result in web_search_results[:3]
            ])

        # Get linguistic rules and patterns
        linguistic_rules = self._get_linguistic_rules(source_lang, target_lang)

        # Create enhanced prompt with all available data sources
        prompt = f"""You are an expert linguist with access to comprehensive translation resources for {source_lang.title()} and {target_lang.title()}.

CRITICAL INSTRUCTIONS:
1. PRIORITIZE translate.py data matches (most authoritative)
2. Use web search results for additional context
3. Apply database examples for grammatical patterns
4. PRESERVE original punctuation and sentence type: {punctuation_info}
5. Focus on GRAMMATICAL STRUCTURE, not word-by-word translation

PUNCTUATION ANALYSIS: {punctuation_info}
GRAMMATICAL ANALYSIS: {grammatical_analysis}

{translate_py_context}

{database_context}

{web_context}

SPECIFIC WORD TRANSLATIONS (CRITICAL - MUST USE THESE):
• kumain → mënama (eat) - NEVER use 'mama' alone
• lalaki → lagëy (man) - NEVER keep as 'lalaki'
• matanda → lukës (old) - NEVER use 'mëlëmëndëg'
• tinapay → ëfan (bread)

GRAMMATICAL PATTERN FOR "kumain ang matandang lalaki ng tinapay":
CORRECT: "Mënama i lukëse lagëy ëfan"
Pattern: VERB + i + ADJECTIVE + NOUN + OBJECT

LINGUISTIC RULES FOR {source_lang.title()} → {target_lang.title()}:
{linguistic_rules}

INPUT TO TRANSLATE: "{text}"

TRANSLATION APPROACH:
1. CHECK translate.py matches first - use exact matches if available
2. APPLY web search context for unfamiliar terms
3. USE specific word translations from the critical list above
4. FOLLOW grammatical pattern: VERB + i + ADJECTIVE + NOUN + OBJECT
5. PRESERVE original punctuation (?, !, ., etc.)
6. CREATE natural {target_lang} expression

EXAMPLES OF CORRECT TRANSLATIONS:
- "kumain ang matandang lalaki ng tinapay" → "Mënama i lukëse lagëy ëfan"
- "Kumusta ka?" → "Faanuy kaame?" (question mark preserved)
- "Maganda!" → "Fiyoy!" (exclamation preserved)

PROVIDE ONLY THE GRAMMATICALLY CORRECT TRANSLATION WITH PROPER PUNCTUATION:"""

        # Try with retries and better error handling
        for attempt in range(self.max_retries):
            try:
                response = self.model.generate_content(prompt)
                if response and response.text:
                    # Clean up the response but preserve punctuation
                    result = response.text.strip()
                    # Remove quotes but keep punctuation
                    if result.startswith('"') and result.endswith('"'):
                        result = result[1:-1]
                    if result.startswith("'") and result.endswith("'"):
                        result = result[1:-1]
                    return result
                else:
                    raise Exception("Empty response from Gemini")

            except Exception as e:
                error_msg = str(e).lower()
                if ("quota" in error_msg or "rate" in error_msg) and attempt < self.max_retries - 1:
                    print(f"Rate limit hit, waiting {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                    continue
                elif attempt < self.max_retries - 1:
                    print(f"API error: {e}, retrying...")
                    time.sleep(1)
                    continue
                else:
                    print(f"Enhanced Gemini API failed after {self.max_retries} attempts: {e}")
                    raise e

    def _analyze_punctuation(self, text):
        """Analyze punctuation and sentence type"""
        if text.endswith('?'):
            return "Interrogative sentence (question) - MUST preserve question mark (?)"
        elif text.endswith('!'):
            return "Exclamatory sentence - MUST preserve exclamation mark (!)"
        elif text.endswith('.'):
            return "Declarative sentence - MUST preserve period (.)"
        else:
            return "Simple statement - add appropriate punctuation if needed"

    def _analyze_grammar(self, text, source_lang):
        """Stub for grammatical analysis. Returns a simple string for now."""
        # In a real implementation, this would use NLP or rules for Tagalog/Teduray/English
        # For now, just return a placeholder
        return f"Basic grammatical analysis for '{text}' in {source_lang}."

    def _api_translate(self, text, source_lang, target_lang, rules):
        """Intelligent translation using Gemini with grammatical rule learning"""
        # First, try to understand the grammatical structure
        grammatical_analysis = self._analyze_grammar(text, source_lang)

        # Get relevant examples that match the grammatical pattern
        pattern_examples = self._get_pattern_examples(text, source_lang, target_lang, grammatical_analysis)

        # Get general examples for context
        general_examples = TranslationRule.objects.filter(
            source_language=source_lang
        ).order_by('?')[:15]

        # Combine all examples
        all_examples = list(pattern_examples) + list(general_examples)

        # Remove duplicates
        seen = set()
        unique_examples = []
        for rule in all_examples:
            key = (rule.source_text, rule.target_text)
            if key not in seen:
                seen.add(key)
                unique_examples.append(rule)

        # Build context with grammatical patterns
        examples_context = "\n".join([
            f"'{rule.source_text}' → '{rule.target_text}'"
            for rule in unique_examples[:30]
        ])

        # Get linguistic rules and patterns
        linguistic_rules = self._get_linguistic_rules(source_lang, target_lang)

        # Create an intelligent prompt that focuses on grammatical understanding
        prompt = f"""You are an expert linguist specializing in {source_lang.title()} and {target_lang.title()} grammar and translation.

CRITICAL INSTRUCTION: Focus on GRAMMATICAL STRUCTURE and MEANING, not word-by-word translation.

GRAMMATICAL ANALYSIS OF INPUT:
{grammatical_analysis}

LINGUISTIC RULES FOR {source_lang.title()} → {target_lang.title()}:
{linguistic_rules}

REFERENCE TRANSLATIONS FROM DATABASE:
{examples_context}

INPUT TO TRANSLATE: "{text}"

TRANSLATION APPROACH:
1. ANALYZE the grammatical structure of the {source_lang} sentence
2. UNDERSTAND the complete meaning and intent
3. APPLY {target_lang} grammatical patterns (not word-by-word)
4. USE database examples as reference for vocabulary
5. CREATE natural, grammatically correct {target_lang} expression

FOR TAGALOG → TEDURAY SPECIFICALLY:
- Understand focus markers (ang/ng/sa) and convert to Teduray equivalents
- Recognize verb aspects and tense
- Handle pronouns and possessives properly
- Maintain natural Teduray word order and expression
- Use appropriate Teduray grammatical markers

EXAMPLES OF GRAMMATICAL TRANSLATION (NOT WORD-BY-WORD):
- "Gusto kong kumain" → "M�uyotu mama" (not "gusto ku kumain")
- "Maganda ang bahay" → "Fiyoy ike lawi" (not "maganda ang bahay")
- "Mahal kita" → "M�uyotu beem" (not "mahal ka")

PROVIDE ONLY THE GRAMMATICALLY CORRECT TRANSLATION:"""

        # Try with retries and better error handling
        for attempt in range(self.max_retries):
            try:
                response = self.model.generate_content(prompt)
                if response and response.text:
                    # Clean up the response
                    result = response.text.strip()
                    # Remove any quotes or extra formatting
                    if result.startswith('"') and result.endswith('"'):
                        result = result[1:-1]
                    if result.startswith("'") and result.endswith("'"):
                        result = result[1:-1]
                    return result
                else:
                    raise Exception("Empty response from Gemini")

            except Exception as e:
                error_msg = str(e).lower()
                if ("quota" in error_msg or "rate" in error_msg) and attempt < self.max_retries - 1:
                    # Rate limit - wait and retry
                    print(f"Rate limit hit, waiting {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                    continue
                elif attempt < self.max_retries - 1:
                    # Other error - shorter wait and retry
                    print(f"API error: {e}, retrying...")
                    time.sleep(1)
                    continue
                else:
                    # Final attempt failed
                    print(f"Gemini API failed after {self.max_retries} attempts: {e}")
                    raise e

    def _search_translate_py_data(self, text, source_lang, target_lang):
        """Stub for searching translate.py data. Returns a list of matches if any."""
        # For now, just do a simple substring match in the loaded data
        matches = []
        for entry in getattr(self, 'translate_py_data', []):
            if text.strip().lower() in entry['source'].strip().lower():
                matches.append(entry)
        return matches

    def _web_fallback_translate(self, text, source_lang, target_lang):
        """Try to find a translation from other online resources if Bible.com or local sources fail."""
        try:
            try:
                import bs4
                from bs4 import BeautifulSoup
            except ImportError:
                print("BeautifulSoup4 (bs4) is not installed. Please install it with 'pip install beautifulsoup4'.")
                return None
            query = f"{text} Tagalog Teduray Bible verse"
            import requests
            search_url = f"https://www.google.com/search?q={requests.utils.quote(query)}"
            headers = {'User-Agent': 'Mozilla/5.0'}
            resp = requests.get(search_url, headers=headers, timeout=10)
            if resp.status_code == 200:
                soup = BeautifulSoup(resp.text, 'html.parser')
                for div in soup.find_all('div', class_='BNeawe s3v9rd AP7Wnd'):
                    snippet = div.get_text()
                    if 'Teduray' in snippet and 'Tagalog' in snippet:
                        return snippet
                    if len(snippet.split()) > 5:
                        return snippet
            return None
        except Exception as e:
            print(f"Web fallback search failed: {e}")
            return None

    def _load_translate_py_data(self):
        """Load translation data from translate.py as a list of dicts: [{'source': ..., 'target': ..., 'match_type': ...}, ...]"""
        data = []
        try:
            import csv
            import codecs
            # Try to read translate.py as a tab-separated file, skipping header lines
            path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'translate.py')
            with codecs.open(path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            for line in lines:
                if line.strip().startswith('#') or not line.strip():
                    continue
                # Heuristic: expect tab-separated lines with at least 3 columns
                parts = line.strip().split('\t')
                if len(parts) == 3:
                    tagalog, english, teduray = parts
                    data.append({'source': tagalog, 'target': teduray, 'match_type': 'translate.py'})
            print(f"Successfully read translate.py with utf-8 encoding\nLoaded {len(data)} translations from translate.py")
        except Exception as e:
            print(f"Failed to load translate.py: {e}")
        return data

    def _fetch_bible_verse(self, book, chapter, verse, lang_code):
        """Fetch a Bible verse from Bible.com for a given language code, supporting parallel Teduray (TIY) and Tagalog (TLAB)."""
        try:
            version_map = {
                'tagalog': 'TLAB',  # Tagalog Ang Biblia
                'teduray': 'TIY', # Teduray (TIY)
                'english': 'KJV', # King James Version
            }
            version = version_map.get(lang_code.lower(), 'TLAB')
            url = f"https://www.bible.com/bible/1266/{book.upper()}.{chapter}.{version}"
            headers = {'User-Agent': 'Mozilla/5.0'}
            resp = requests.get(url, headers=headers, timeout=10)
            if resp.status_code != 200:
                return None
            # Extract the verse using the current Bible.com HTML structure
            verse_pattern = re.compile(rf'<span[^>]+class="verse"[^>]+data-usfm="{book.upper()}\\.{chapter}\\.{verse}"[^>]*>.*?</span>\\s*<span[^>]+class="content"[^>]*>(.*?)</span>', re.DOTALL)
            match = verse_pattern.search(resp.text)
            if match:
                verse_html = match.group(1)
                verse_text = re.sub(r'<[^>]+>', '', verse_html)
                return verse_text.strip()
            # Fallback: try to extract verse from meta tag or other patterns
            match2 = re.search(r'<meta name="description" content="([^"]+)"', resp.text)
            if match2:
                return match2.group(1).strip()
            return None
        except Exception as e:
            print(f"Bible.com fetch failed: {e}")
            return None

    def _get_pattern_examples(self, text, source_lang, target_lang, grammatical_analysis):
        """Stub for getting pattern examples. Returns an empty list for now."""
        # In a real implementation, this would return relevant TranslationRule objects
        return []

    def _real_web_search(self, query, max_results=3):
        """Perform a real web search using SerpAPI and return a list of dicts with 'title' and 'snippet'."""
        try:
            import requests
            serpapi_key = "df39f706f7af4b3ac610d27b3ff8c23a1e3aa8765f574f59eece16cf86a41cdf"
            params = {
                "q": query,
                "api_key": serpapi_key,
                "engine": "google",
                "num": max_results,
                "hl": "en",
            }
            url = "https://serpapi.com/search"
            resp = requests.get(url, params=params, timeout=10)
            if resp.status_code != 200:
                print(f"SerpAPI error: status {resp.status_code}")
                return []
            data = resp.json()
            results = []
            # Prefer organic_results, fallback to other fields if needed
            for item in data.get("organic_results", [])[:max_results]:
                title = item.get("title", "")
                snippet = item.get("snippet", "")
                if title or snippet:
                    results.append({"title": title, "snippet": snippet})
            # Fallback: try answer_box or other fields if no organic results
            if not results and "answer_box" in data:
                ab = data["answer_box"]
                title = ab.get("title", "")
                snippet = ab.get("snippet", "") or ab.get("answer", "")
                if title or snippet:
                    results.append({"title": title, "snippet": snippet})
            return results
        except Exception as e:
            print(f"SerpAPI web search failed: {e}")
            return []

    def _get_linguistic_rules(self, source_lang, target_lang):
        """Stub for linguistic rules. Returns an empty string for now."""
        # In a real implementation, this would return language-specific rules
        return ""

    def _multi_step_advanced_translation(self, text, source_lang, target_lang, rules):
        """
        Advanced multi-step translation pipeline as per ENHANCED_TRANSLATION_METHODS.md:
        1. Grammatical and semantic analysis of input (Tagalog/Teduray/English)
        2. Phrase/word segmentation and lookup (translate.py, database)
        3. Syntactic reordering and marker adjustment (focus, aspect, visibility, etc.)
        4. Cultural/idiomatic refinement (avoid literalism, use natural Teduray)
        5. Validation: ensure output is idiomatic, grammatical, and culturally appropriate
        6. Final Gemini pass for naturalness (with strict prompt to avoid meta-output)
        """
        # Step 1: Grammatical and semantic analysis
        grammatical_analysis = self._analyze_grammar(text, source_lang)
        # Step 2: Phrase/word segmentation and lookup
        py_match = self._find_translate_py_match(text, source_lang.lower(), target_lang.lower())
        if py_match:
            return self._preserve_punctuation(text, py_match)
        db_match = self._find_flexible_match(text, source_lang)
        if db_match:
            return self._preserve_punctuation(text, db_match.target_text)
        # Step 3: Syntactic reordering and marker adjustment (stub: could use rules)
        # For now, just pass through to Gemini with explicit instructions
        # Step 4: Cultural/idiomatic refinement and validation
        # Step 5: Final Gemini pass for naturalness
        linguistic_rules = self._get_linguistic_rules(source_lang, target_lang)
        prompt = f"""You are a professional Teduray translator. Translate the following {source_lang} text to natural, idiomatic, and grammatically correct {target_lang}.\n\n- DO NOT explain, comment, or output anything except the translation.\n- Use Teduray Bible style and cultural norms if relevant.\n- Avoid literal word-for-word translation.\n- Use correct Teduray focus, aspect, and visibility markers.\n- If the input is a Bible verse, match Teduray Bible style.\n- If the input is a phrase or sentence, use natural Teduray.\n- Preserve original punctuation.\n- Output ONLY the translation, nothing else.\n\nLINGUISTIC RULES:\n{linguistic_rules}\n\nGRAMMATICAL ANALYSIS:\n{grammatical_analysis}\n\nINPUT:\n{text}\n\nTRANSLATION:"""
        for attempt in range(self.max_retries):
            try:
                response = self.model.generate_content(prompt)
                if response and response.text:
                    result = response.text.strip()
                    # Remove quotes and meta-output
                    if result.startswith('"') and result.endswith('"'):
                        result = result[1:-1]
                    if result.startswith("'") and result.endswith("'"):
                        result = result[1:-1]
                    # Remove meta-linguistic output if present
                    result = re.sub(r'^(Translation:|Teduray:|Output:)', '', result, flags=re.I).strip()
                    return self._preserve_punctuation(text, result)
                else:
                    raise Exception("Empty response from Gemini")
            except Exception as e:
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    print(f"Multi-step Gemini translation failed: {e}")
                    raise e

    def translate(self, text, source_lang, target_lang):
        """Main translation method with advanced multi-step pipeline."""
        # Step 1: Official Teduray Bible verse match (return immediately if found)
        # Try to detect if input is a Bible verse reference and look up in Teduray Bible corpus
        # (You may want to improve this detection logic as needed)
        bible_match = None
        # Example: try to parse as (book, chapter, verse) or use your own detection logic
        # For now, just check if the text matches a known verse in the corpus
        for (book, chapter, verse), teduray_text in self._teduray_bible_corpus.items():
            if text.strip().lower() == teduray_text.strip().lower():
                bible_match = teduray_text
                break
        if bible_match:
            return bible_match

        # Step 2: Database exact match (do not return yet)
        exact_match = self._find_flexible_match(text, source_lang)
        # Step 3: translate.py direct match (do not return yet)
        py_match = self._find_translate_py_match(text, source_lang.lower(), target_lang.lower())
        # Step 4: Enhanced single word/phrase translation (dictionary)
        all_rules = TranslationRule.objects.filter(source_language=source_lang)
        dictionary_result = None
        if len(text.split()) <= 3:
            dictionary_result = self._dictionary_translate(text, all_rules)
            if dictionary_result and not self._is_good_translation(text, dictionary_result):
                dictionary_result = None

        # Step 5: Advanced comprehensive translation (multi-step, always use Gemini for non-Bible cases)
        # Use the best available match as input to Gemini, or the original text if no match
        base_translation = py_match or (exact_match.target_text if exact_match else None) or dictionary_result or text
        try:
            advanced_result = self._multi_step_advanced_translation(base_translation, source_lang, target_lang, all_rules)
            if advanced_result:
                return advanced_result
        except Exception as e:
            print(f"Advanced multi-step translation failed: {e}")
        # Step 6: Gemini fallback (should rarely be needed)
        try:
            api_result = self._enhanced_api_translate(text, source_lang, target_lang, all_rules)
            if api_result and not api_result.startswith("[Translation limited]"):
                return api_result
        except Exception as e:
            print(f"Gemini API translation failed: {e}")
        # Step 7: Web search fallback
        try:
            web_result = self._web_fallback_translate(text, source_lang, target_lang)
            if web_result:
                return web_result
        except Exception as e:
            print(f"Web translation failed: {e}")
        # Step 8: Last resort
        return f"[No translation available] {text}"