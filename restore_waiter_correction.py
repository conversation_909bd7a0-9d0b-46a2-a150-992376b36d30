#!/usr/bin/env python
"""
Restore the correct waiter translation and test the protection system
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def restore_correct_waiter_translation():
    """Restore the correct waiter translation"""
    print("🔧 Restoring Correct Waiter Translation")
    print("=" * 60)
    
    try:
        from translation_app.models import TranslationFeedback, TranslationRule
        
        original_text = "Inirerekomenda ng waiter ang pagkaing ito."
        correct_translation = "Nagrekomenday waiterey ni amaën."
        
        print(f"📝 Restoring correct translation:")
        print(f"   Original: '{original_text}'")
        print(f"   Correct: '{correct_translation}'")
        
        # Update feedback
        feedback = TranslationFeedback.objects.filter(
            source_text__iexact=original_text,
            source_language='tagalog',
            target_language='teduray'
        ).first()
        
        if feedback:
            feedback.translated_text = correct_translation
            feedback.feedback_type = 'correction'
            feedback.save()
            print(f"   ✅ Updated feedback")
        else:
            TranslationFeedback.objects.create(
                source_text=original_text,
                source_language='tagalog',
                target_language='teduray',
                translated_text=correct_translation,
                feedback_type='correction'
            )
            print(f"   ✅ Created new feedback")
        
        # Update rule
        rule = TranslationRule.objects.filter(
            source_language='tagalog',
            source_text__iexact=original_text
        ).first()
        
        if rule:
            rule.target_text = correct_translation
            rule.save()
            print(f"   ✅ Updated rule")
        else:
            TranslationRule.objects.create(
                source_language='tagalog',
                source_text=original_text,
                target_text=correct_translation
            )
            print(f"   ✅ Created new rule")
        
        print(f"\n✅ Correct waiter translation restored!")
        return True
        
    except Exception as e:
        print(f"❌ Error restoring waiter translation: {e}")
        return False

def test_protection_system():
    """Test that the system protects user corrections from being overwritten"""
    print("\n🛡️ Testing Protection System")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        test_sentence = "Inirerekomenda ng waiter ang pagkaing ito."
        correct_translation = "Nagrekomenday waiterey ni amaën."
        
        print(f"🔍 Testing protection for: '{test_sentence}'")
        print(f"   Expected: '{correct_translation}'")
        
        # Test 1: Check if user correction exists
        has_correction = service._has_user_correction(test_sentence, "tagalog", "teduray")
        print(f"   Has user correction: {'✅ Yes' if has_correction else '❌ No'}")
        
        # Test 2: Get translation (should use user correction)
        result = service.translate(test_sentence, "tagalog", "teduray")
        print(f"   Translation result: '{result}'")
        
        # Test 3: Check if it matches user correction
        matches_correction = result.lower() == correct_translation.lower()
        print(f"   Matches user correction: {'✅ Yes' if matches_correction else '❌ No'}")
        
        if has_correction and matches_correction:
            print(f"   🎉 EXCELLENT! Protection system working!")
            return True
        elif has_correction:
            print(f"   ⚠️ User correction exists but not being used")
            return False
        else:
            print(f"   ❌ No user correction found")
            return False
            
    except Exception as e:
        print(f"❌ Protection system test error: {e}")
        return False

def test_learning_prevention():
    """Test that system doesn't learn when user corrections exist"""
    print("\n🚫 Testing Learning Prevention")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationRule
        
        service = TranslationService()
        
        test_sentence = "Inirerekomenda ng waiter ang pagkaing ito."
        
        print(f"🔍 Testing learning prevention for: '{test_sentence}'")
        
        # Count rules before
        initial_count = TranslationRule.objects.filter(
            source_language='tagalog',
            source_text__iexact=test_sentence
        ).count()
        print(f"   Initial rules: {initial_count}")
        
        # Try to translate (this might trigger Gemini)
        result = service.translate(test_sentence, "tagalog", "teduray")
        print(f"   Translation: '{result}'")
        
        # Count rules after
        final_count = TranslationRule.objects.filter(
            source_language='tagalog',
            source_text__iexact=test_sentence
        ).count()
        print(f"   Final rules: {final_count}")
        
        # Check if new rules were added
        new_rules_added = final_count > initial_count
        
        if new_rules_added:
            print(f"   ❌ System added new rules (should not happen)")
            return False
        else:
            print(f"   ✅ System did not add new rules (correct behavior)")
            return True
            
    except Exception as e:
        print(f"❌ Learning prevention test error: {e}")
        return False

def test_multiple_translations():
    """Test multiple translations to ensure consistency"""
    print("\n🔄 Testing Multiple Translations")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        test_sentence = "Inirerekomenda ng waiter ang pagkaing ito."
        correct_translation = "Nagrekomenday waiterey ni amaën."
        
        print(f"🔍 Testing consistency for: '{test_sentence}'")
        print(f"   Expected: '{correct_translation}'")
        
        # Test multiple times
        results = []
        for i in range(3):
            result = service.translate(test_sentence, "tagalog", "teduray")
            results.append(result)
            print(f"   Translation {i+1}: '{result}'")
        
        # Check consistency
        all_same = all(r.lower() == results[0].lower() for r in results)
        all_correct = all(r.lower() == correct_translation.lower() for r in results)
        
        print(f"   All translations same: {'✅ Yes' if all_same else '❌ No'}")
        print(f"   All translations correct: {'✅ Yes' if all_correct else '❌ No'}")
        
        if all_same and all_correct:
            print(f"   🎉 EXCELLENT! Consistent and correct!")
            return True
        elif all_same:
            print(f"   ⚠️ Consistent but not correct")
            return False
        else:
            print(f"   ❌ Inconsistent translations")
            return False
            
    except Exception as e:
        print(f"❌ Multiple translations test error: {e}")
        return False

def explain_protection_system():
    """Explain how the protection system works"""
    print("\n📚 Protection System Explanation")
    print("=" * 60)
    
    print("🛡️ USER CORRECTION PROTECTION SYSTEM:")
    print("   1. ✅ User corrections have HIGHEST PRIORITY")
    print("   2. ✅ System checks for user corrections FIRST")
    print("   3. ✅ If user correction exists, use it immediately")
    print("   4. ✅ Skip all other translation methods")
    print("   5. ✅ Prevent learning when user corrections exist")
    
    print(f"\n🔧 PROTECTION MECHANISMS:")
    print("   • _check_user_corrections() - Finds user feedback")
    print("   • _has_user_correction() - Checks if correction exists")
    print("   • Learning prevention - Stops overwriting corrections")
    print("   • Priority system - User corrections override everything")
    
    print(f"\n📝 YOUR WAITER EXAMPLE:")
    print("   Original: 'Inirerekomenda ng waiter ang pagkaing ito.'")
    print("   Your correction: 'Nagrekomenday waiterey ni amaën.'")
    print("   System behavior: Uses YOUR correction every time")
    
    print(f"\n🎯 WHAT HAPPENS NOW:")
    print("   1. ✅ System finds your correction in database")
    print("   2. ✅ Returns your correction immediately")
    print("   3. ✅ Skips Gemini and other methods")
    print("   4. ✅ Prevents learning from overwriting")
    print("   5. ✅ Consistent results every time")
    
    print(f"\n🚫 WHAT DOESN'T HAPPEN:")
    print("   • ❌ Gemini doesn't override your correction")
    print("   • ❌ System doesn't learn wrong translations")
    print("   • ❌ Your corrections don't get overwritten")
    print("   • ❌ No inconsistent results")
    
    return True

if __name__ == "__main__":
    print("🛡️ User Correction Protection System Test")
    print("=" * 70)
    print("Testing protection of user corrections from being overwritten")
    print("=" * 70)
    
    # Run protection tests
    restoration = restore_correct_waiter_translation()
    protection = test_protection_system()
    learning_prevention = test_learning_prevention()
    consistency = test_multiple_translations()
    explanation = explain_protection_system()
    
    print("\n" + "=" * 70)
    print("📊 PROTECTION SYSTEM RESULTS")
    print("=" * 70)
    
    results = {
        "Restoration": restoration,
        "Protection System": protection,
        "Learning Prevention": learning_prevention,
        "Consistency Test": consistency,
        "Explanation": explanation
    }
    
    for test_name, result in results.items():
        status = "✅ WORKING" if result else "❌ NEEDS WORK"
        print(f"{test_name}: {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    protection_score = (working_count / total_count) * 100
    
    print(f"\n📈 Protection System Score: {protection_score:.1f}%")
    
    if working_count == total_count:
        print(f"\n🎉 PERFECT PROTECTION SYSTEM!")
        print("Your corrections are now fully protected!")
        print("\n💡 Protection Achievements:")
        print("  • ✅ Correct translation restored")
        print("  • ✅ Protection system working")
        print("  • ✅ Learning prevention active")
        print("  • ✅ Consistent results")
        print("  • ✅ Complete protection explanation")
        print("\n🛡️ Your waiter correction is now protected:")
        print("  • 'Inirerekomenda ng waiter ang pagkaing ito.'")
        print("  • → 'Nagrekomenday waiterey ni amaën.'")
        print("  • Cannot be overwritten by Gemini!")
        print("  • Consistent every time!")
    elif protection_score >= 80:
        print(f"\n🎯 EXCELLENT! {protection_score:.1f}% protection working!")
        print("User corrections are well protected")
    elif protection_score >= 60:
        print(f"\n✅ GOOD! {protection_score:.1f}% protection working!")
        print("User corrections are mostly protected")
    else:
        print(f"\n⚠️ {protection_score:.1f}% protection working - needs work")
        print("User corrections need better protection")
    
    print(f"\n🛡️ User Correction Protection: {'FULLY ACTIVE' if protection_score >= 80 else 'NEEDS IMPROVEMENT'}")
    print("=" * 70)
