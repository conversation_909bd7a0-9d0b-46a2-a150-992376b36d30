#!/usr/bin/env python
"""
Test the enhanced web search + Gemini system for authentic translations
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_web_gemini_integration():
    """Test the web search + Gemini integration for authentic translations"""
    print("🌐 Testing Web Search + Gemini Integration")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test words that you mentioned are incorrect
        test_cases = [
            ("bata", "Child - needs correct Teduray translation"),
            ("anak", "Child/offspring - needs correct Teduray translation"),
            ("lalaki", "Man - verify if 'lagey' is correct"),
            ("babae", "Woman - find authentic Teduray word"),
            ("masaya", "Happy - verify if 'mefiya' is correct"),
            ("kumain", "Eat - verify if 'menama' is correct"),
        ]
        
        print("🔍 Testing words that need verification:")
        
        for word, description in test_cases:
            print(f"\n📝 Testing: '{word}' ({description})")
            
            # Test web search + Gemini translation
            web_gemini_result = service._web_search_gemini_translate(word, "tagalog", "teduray")
            
            if web_gemini_result:
                print(f"   🌐 Web + Gemini result: '{web_gemini_result}'")
                
                # Compare with current system
                current_result = service.translate(word, "tagalog", "teduray")
                print(f"   🔄 Current system: '{current_result}'")
                
                if web_gemini_result != current_result:
                    print(f"   ⚠️  Different results - Web+Gemini may have found better translation")
                else:
                    print(f"   ✅ Results match")
            else:
                print(f"   ❌ Web + Gemini failed to find translation")
                
                # Fallback to current system
                current_result = service.translate(word, "tagalog", "teduray")
                print(f"   🔄 Current system fallback: '{current_result}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_verification_system():
    """Test the translation verification system"""
    print("\n🔍 Testing Translation Verification System")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test verification of potentially incorrect translations
        verification_tests = [
            ("bata", "bata", "Is 'bata' correct Teduray for 'child'?"),
            ("anak", "anak", "Is 'anak' correct Teduray for 'child/offspring'?"),
            ("lalaki", "lagey", "Is 'lagey' correct Teduray for 'man'?"),
            ("masaya", "mefiya", "Is 'mefiya' correct Teduray for 'happy'?"),
        ]
        
        print("🔍 Testing translation verification:")
        
        for original, proposed, question in verification_tests:
            print(f"\n📝 {question}")
            print(f"   Original: '{original}' → Proposed: '{proposed}'")
            
            # Test verification
            verified = service._verify_translation_with_web_and_gemini(
                original, proposed, "tagalog", "teduray"
            )
            
            if verified:
                if verified == proposed:
                    print(f"   ✅ Verified as correct: '{verified}'")
                else:
                    print(f"   🔄 Corrected to: '{verified}'")
            else:
                print(f"   ⚠️  Could not verify translation")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification test error: {e}")
        return False

def test_learning_system():
    """Test the learning system for new authentic translations"""
    print("\n📚 Testing Learning System")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationRule
        
        service = TranslationService()
        
        # Count current rules
        initial_count = TranslationRule.objects.filter(source_language='tagalog').count()
        print(f"📊 Initial translation rules: {initial_count}")
        
        # Test learning from a new authentic translation
        test_original = "test_word_for_learning"
        test_translation = "test_teduray_word"
        
        print(f"\n🔍 Testing learning with: '{test_original}' → '{test_translation}'")
        
        # Simulate learning
        service._learn_from_authentic_translation(
            test_original, test_translation, "tagalog", "teduray"
        )
        
        # Check if it was learned
        new_count = TranslationRule.objects.filter(source_language='tagalog').count()
        
        if new_count > initial_count:
            print(f"   ✅ Successfully learned new translation")
            print(f"   📊 New rule count: {new_count}")
            
            # Clean up test data
            TranslationRule.objects.filter(
                source_text=test_original,
                target_text=test_translation
            ).delete()
            print(f"   🧹 Cleaned up test data")
        else:
            print(f"   ⚠️  Learning may not have worked")
        
        return True
        
    except Exception as e:
        print(f"❌ Learning test error: {e}")
        return False

def test_complete_pipeline():
    """Test the complete enhanced pipeline"""
    print("\n🔄 Testing Complete Enhanced Pipeline")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test with a sentence that should trigger web search + Gemini
        test_sentence = "Ang bata ay masaya"
        
        print(f"🔍 Testing complete pipeline with: '{test_sentence}'")
        
        # Clear cache to ensure fresh translation
        try:
            service.cache_manager.clear_cache()
            print("   🧹 Cache cleared")
        except:
            print("   ⚠️  Could not clear cache")
        
        # Translate using the enhanced pipeline
        result = service.translate(test_sentence, "tagalog", "teduray")
        
        print(f"   📤 Final result: '{result}'")
        
        # Check translation quality
        quality_data = service.quality_scorer.score_translation(
            test_sentence, result, "tagalog", "teduray", 'enhanced_pipeline'
        )
        
        print(f"   📊 Quality score: {quality_data.get('overall_score', 0.5):.1%}")
        print(f"   📊 Quality grade: {quality_data.get('grade', 'C')}")
        
        # Check if it used web search + Gemini
        stats = service.get_translation_statistics()
        gemini_calls = stats.get('gemini_calls', 0)
        
        if gemini_calls > 0:
            print(f"   ✅ Used Gemini API ({gemini_calls} calls)")
        else:
            print(f"   ℹ️  Did not use Gemini API")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_authentic_word_search():
    """Test searching for authentic Teduray words"""
    print("\n🔍 Testing Authentic Word Search")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test searching for authentic translations
        search_words = ["child", "happy", "eat", "man", "woman"]
        
        print("🔍 Searching for authentic Teduray words:")
        
        for word in search_words:
            print(f"\n📝 Searching for Teduray word for '{word}':")
            
            # Search using web search
            web_results = service.web_search.search_translation(word, "english", "teduray", "linguistic")
            
            if web_results:
                print(f"   🌐 Found {len(web_results)} web results")
                
                # Extract potential translations
                for i, result in enumerate(web_results[:3]):
                    extracted = service._extract_teduray_from_result(result, word)
                    if extracted:
                        print(f"     Result {i+1}: {extracted}")
                    else:
                        print(f"     Result {i+1}: No translations extracted")
            else:
                print(f"   ❌ No web results found")
        
        return True
        
    except Exception as e:
        print(f"❌ Search test error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Enhanced Web Search + Gemini System Test")
    print("=" * 70)
    print("Testing the new system for finding authentic Teduray translations")
    print("=" * 70)
    
    # Run tests
    web_gemini_test = test_web_gemini_integration()
    verification_test = test_verification_system()
    learning_test = test_learning_system()
    pipeline_test = test_complete_pipeline()
    search_test = test_authentic_word_search()
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS")
    print("=" * 70)
    
    if web_gemini_test:
        print("✅ Web + Gemini Integration: PASSED")
    else:
        print("❌ Web + Gemini Integration: FAILED")
    
    if verification_test:
        print("✅ Translation Verification: PASSED")
    else:
        print("❌ Translation Verification: FAILED")
    
    if learning_test:
        print("✅ Learning System: PASSED")
    else:
        print("❌ Learning System: FAILED")
    
    if pipeline_test:
        print("✅ Complete Pipeline: PASSED")
    else:
        print("❌ Complete Pipeline: FAILED")
    
    if search_test:
        print("✅ Authentic Word Search: PASSED")
    else:
        print("❌ Authentic Word Search: FAILED")
    
    if all([web_gemini_test, verification_test, learning_test, pipeline_test, search_test]):
        print("\n🎉 ALL TESTS PASSED!")
        print("The enhanced system is ready to:")
        print("  • Search for authentic Teduray translations")
        print("  • Verify translations with Gemini")
        print("  • Learn from authentic sources")
        print("  • Correct incorrect translations")
        print("  • Provide better quality translations")
    else:
        print("\n⚠️  Some tests failed. The system needs further refinement.")
    
    print("\n💡 This system will help correct words like 'bata' and 'anak'")
    print("   by finding authentic Teduray translations from web sources!")
    
    print("\n" + "=" * 70)
