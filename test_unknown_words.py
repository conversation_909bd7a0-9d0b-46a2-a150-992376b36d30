#!/usr/bin/env python
"""
Test the enhanced system with unknown words and improved web search
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_unknown_words_handling():
    """Test how the system handles words not in the database"""
    print("🔍 Testing Unknown Words Handling")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test cases with words that might not be in the database
        test_cases = [
            ("kompyuter", "Computer (modern word)"),
            ("telepono", "Telephone (modern word)"),
            ("sasakyan", "Vehicle (common word)"),
            ("paaralan", "School (common word)"),
            ("Kumain ng mansanas ang bata", "Sentence with 'apple' (mansanas)"),
            ("Naglalaro ng basketball", "Playing basketball (sport)"),
            ("Mag-aral ng matematika", "Study mathematics (subject)"),
        ]
        
        for text, description in test_cases:
            print(f"\n🔍 Testing: {description}")
            print(f"   Input: '{text}'")
            
            # Check if words are unknown
            unknown_words = service._identify_unknown_words(text, "tagalog")
            print(f"   Unknown words: {unknown_words}")
            
            # Test the translation
            result = service.translate(text, "tagalog", "teduray")
            print(f"   Translation: '{result}'")
            
            # Check if it used unknown words handling
            if unknown_words:
                print(f"   ✅ System detected unknown words and should search for them")
            else:
                print(f"   ℹ️  All words found in database/translate.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_search_enhancement():
    """Test the enhanced web search for Teduray content"""
    print("\n🌐 Testing Enhanced Web Search")
    print("=" * 50)
    
    try:
        from translation_app.web_search_service import WebSearchService
        
        search_service = WebSearchService()
        
        # Test search queries for Teduray content
        test_words = ["kumain", "lalaki", "kompyuter"]
        
        for word in test_words:
            print(f"\n🔍 Searching for: '{word}'")
            
            # Test linguistic search (should focus on Teduray translations)
            results = search_service.search_translation(word, "tagalog", "teduray", "linguistic")
            
            print(f"   Found {len(results)} results")
            
            for i, result in enumerate(results[:3]):
                print(f"   Result {i+1}:")
                print(f"     Title: {result.get('title', '')[:60]}...")
                print(f"     Snippet: {result.get('snippet', '')[:100]}...")
                print(f"     Relevance: {result.get('relevance_score', 0):.2f}")
                
                # Check for extracted translations
                extracted = result.get('extracted_translations', [])
                if extracted:
                    print(f"     Extracted translations: {extracted}")
                else:
                    print(f"     No translations extracted")
        
        return True
        
    except Exception as e:
        print(f"❌ Web search test error: {e}")
        return False

def test_teduray_search_queries():
    """Test the Teduray-specific search query generation"""
    print("\n📝 Testing Teduray Search Query Generation")
    print("=" * 50)
    
    try:
        from translation_app.web_search_service import WebSearchService
        
        search_service = WebSearchService()
        
        test_word = "kompyuter"
        
        # Test different search types
        search_types = ["general", "linguistic", "cultural"]
        
        for search_type in search_types:
            print(f"\n🔍 {search_type.title()} search queries for '{test_word}':")
            
            queries = search_service._generate_search_queries(test_word, "tagalog", "teduray", search_type)
            
            for i, query in enumerate(queries, 1):
                print(f"   {i}. {query}")
        
        return True
        
    except Exception as e:
        print(f"❌ Query generation test error: {e}")
        return False

def test_complete_unknown_word_pipeline():
    """Test the complete pipeline for unknown words"""
    print("\n🔄 Testing Complete Unknown Word Pipeline")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test with a sentence that likely has unknown words
        test_text = "Bumili ng kompyuter sa tindahan"
        
        print(f"🔍 Testing complete pipeline for: '{test_text}'")
        
        # Step 1: Identify unknown words
        unknown_words = service._identify_unknown_words(test_text, "tagalog")
        print(f"   Step 1 - Unknown words: {unknown_words}")
        
        # Step 2: Search for Teduray translations
        if unknown_words:
            print(f"   Step 2 - Searching for Teduray translations...")
            for word in unknown_words:
                teduray_result = service._search_teduray_translation(word, "tagalog", "teduray")
                if teduray_result:
                    print(f"     Found: '{word}' → '{teduray_result}'")
                else:
                    print(f"     Not found: '{word}'")
        
        # Step 3: Full translation
        print(f"   Step 3 - Full translation:")
        result = service.translate(test_text, "tagalog", "teduray")
        print(f"     Result: '{result}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Enhanced Translation System Test")
    print("=" * 60)
    print("Testing unknown words handling and enhanced web search")
    print("=" * 60)
    
    # Run tests
    unknown_test = test_unknown_words_handling()
    web_test = test_web_search_enhancement()
    query_test = test_teduray_search_queries()
    pipeline_test = test_complete_unknown_word_pipeline()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    if unknown_test:
        print("✅ Unknown Words Test: PASSED")
    else:
        print("❌ Unknown Words Test: FAILED")
    
    if web_test:
        print("✅ Web Search Enhancement Test: PASSED")
    else:
        print("❌ Web Search Enhancement Test: FAILED")
    
    if query_test:
        print("✅ Query Generation Test: PASSED")
    else:
        print("❌ Query Generation Test: FAILED")
    
    if pipeline_test:
        print("✅ Complete Pipeline Test: PASSED")
    else:
        print("❌ Complete Pipeline Test: FAILED")
    
    if unknown_test and web_test and query_test and pipeline_test:
        print("\n🎉 ALL TESTS PASSED!")
        print("The enhanced system can now:")
        print("  • Identify unknown words not in database/translate.py")
        print("  • Search specifically for Teduray translations")
        print("  • Extract Teduray words from search results")
        print("  • Learn and cache new translations")
        print("  • Provide better web search results focused on Teduray content")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
    
    print("\n" + "=" * 60)
