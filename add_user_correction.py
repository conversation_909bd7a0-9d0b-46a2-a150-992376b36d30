#!/usr/bin/env python
"""
Add user correction to the system
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def add_user_correction():
    """Add the user's correction to the system"""
    print("👥 Adding User Correction to System")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationRule
        
        service = TranslationService()
        
        # User's correction
        original_text = "ang batang masipag ay nag walis"
        user_correction = "i ngae megeror kemenodos"
        
        print(f"📝 User Correction:")
        print(f"   Original: '{original_text}'")
        print(f"   System gave: 'Mëngwalis i ënga mëfiya.' (Grade D - 55.6%)")
        print(f"   User corrected to: '{user_correction}'")
        
        # Add the correction to database
        print(f"\n🔄 Adding correction to database...")
        
        # Check if exists
        exists = TranslationRule.objects.filter(
            source_language='tagalog',
            source_text__iexact=original_text
        ).exists()
        
        if exists:
            print(f"   ℹ️  Rule already exists, updating...")
            rule = TranslationRule.objects.filter(
                source_language='tagalog',
                source_text__iexact=original_text
            ).first()
            rule.target_text = user_correction
            rule.save()
        else:
            print(f"   ✅ Adding new rule...")
            service._learn_from_authentic_translation(original_text, user_correction, "tagalog", "teduray")
        
        # Add individual vocabulary corrections
        vocabulary_corrections = [
            ("bata", "ënga"),
            ("batang", "ënga"),
            ("nagwalis", "kemenodos"),
            ("masipag", "megeror")
        ]
        
        print(f"\n📚 Adding vocabulary corrections:")
        for tagalog_word, teduray_word in vocabulary_corrections:
            print(f"   Adding: '{tagalog_word}' → '{teduray_word}'")
            service._learn_from_authentic_translation(tagalog_word, teduray_word, "tagalog", "teduray")
        
        print(f"\n✅ User correction successfully added to system!")
        return True
        
    except Exception as e:
        print(f"❌ Error adding user correction: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_corrected_translation():
    """Test if the corrected translation now works"""
    print("\n🧪 Testing Corrected Translation")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test the original problematic sentence
        test_sentence = "ang batang masipag ay nag walis"
        
        print(f"🔍 Testing: '{test_sentence}'")
        print(f"   Expected: 'i ngae megeror kemenodos' (user correction)")
        
        result = service.translate(test_sentence, "tagalog", "teduray")
        print(f"   System result: '{result}'")
        
        # Check if it uses user's vocabulary
        user_words_found = 0
        user_vocabulary = ['ënga', 'megeror', 'kemenodos']
        
        for word in user_vocabulary:
            if word.lower() in result.lower():
                print(f"     ✅ Uses user word: {word}")
                user_words_found += 1
            else:
                print(f"     ❌ Missing user word: {word}")
        
        # Check if it matches user correction
        user_correction = "i ngae megeror kemenodos"
        if result.lower() == user_correction.lower():
            print(f"   🎉 PERFECT MATCH with user correction!")
            return True
        elif user_words_found >= 2:
            print(f"   ✅ GOOD - Uses {user_words_found}/3 user vocabulary words")
            return True
        elif user_words_found >= 1:
            print(f"   ⚠️ PARTIAL - Uses {user_words_found}/3 user vocabulary words")
            return True
        else:
            print(f"   ❌ POOR - Not using user corrections")
            return False
            
    except Exception as e:
        print(f"❌ Testing error: {e}")
        return False

def test_individual_words():
    """Test individual word corrections"""
    print("\n📝 Testing Individual Word Corrections")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test individual words
        word_tests = [
            ("bata", "ënga"),
            ("batang", "ënga"),
            ("nagwalis", "kemenodos"),
            ("masipag", "megeror")
        ]
        
        print("🔍 Testing individual word corrections:")
        
        successful_words = 0
        for tagalog_word, expected_teduray in word_tests:
            print(f"\n📝 Testing: '{tagalog_word}'")
            print(f"   Expected: '{expected_teduray}'")
            
            result = service.translate(tagalog_word, "tagalog", "teduray")
            print(f"   Result: '{result}'")
            
            if expected_teduray.lower() in result.lower():
                print(f"   ✅ CORRECT!")
                successful_words += 1
            else:
                print(f"   ❌ INCORRECT")
        
        success_rate = successful_words / len(word_tests)
        print(f"\n📊 Individual word success: {success_rate:.1%} ({successful_words}/{len(word_tests)})")
        
        if success_rate >= 0.75:
            print("🎉 EXCELLENT! Individual words working!")
            return True
        elif success_rate >= 0.5:
            print("✅ GOOD! Most individual words working!")
            return True
        else:
            print("⚠️ Individual words need improvement")
            return False
            
    except Exception as e:
        print(f"❌ Individual word test error: {e}")
        return False

def test_fast_replacement_with_corrections():
    """Test fast replacement with user corrections"""
    print("\n⚡ Testing Fast Replacement with User Corrections")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test fast replacement directly
        test_sentence = "ang batang masipag ay nag walis"
        
        print(f"🔍 Testing fast replacement with: '{test_sentence}'")
        
        result = service._fast_authentic_word_replacement(test_sentence, "tagalog", "teduray")
        
        if result:
            print(f"   Fast replacement result: '{result}'")
            
            # Check for user corrections
            user_corrections = ['ënga', 'megeror', 'kemenodos']
            found_corrections = 0
            
            for correction in user_corrections:
                if correction.lower() in result.lower():
                    print(f"     ✅ Uses correction: {correction}")
                    found_corrections += 1
                else:
                    print(f"     ❌ Missing correction: {correction}")
            
            correction_rate = found_corrections / len(user_corrections)
            print(f"   📊 Correction usage: {correction_rate:.1%}")
            
            if correction_rate >= 0.7:
                print("🎉 EXCELLENT! Fast replacement using corrections!")
                return True
            elif correction_rate >= 0.5:
                print("✅ GOOD! Fast replacement partially using corrections!")
                return True
            else:
                print("⚠️ Fast replacement not using corrections")
                return False
        else:
            print("❌ Fast replacement failed")
            return False
            
    except Exception as e:
        print(f"❌ Fast replacement test error: {e}")
        return False

def explain_user_feedback_system():
    """Explain how user feedback should work"""
    print("\n📚 How User Feedback System Should Work")
    print("=" * 60)
    
    print("🎯 WHEN USER PROVIDES CORRECTION:")
    print("   1. ✅ System saves correction to database")
    print("   2. ✅ Individual words are learned")
    print("   3. ✅ Fast replacement is updated")
    print("   4. ✅ Gemini learns from correction")
    print("   5. ✅ Future translations improve")
    
    print(f"\n📝 YOUR CORRECTION EXAMPLE:")
    print("   Original: 'ang batang masipag ay nag walis'")
    print("   System: 'Mëngwalis i ënga mëfiya.' (Grade D)")
    print("   Your correction: 'i ngae megeror kemenodos'")
    
    print(f"\n📚 VOCABULARY LEARNED:")
    print("   • bata/batang → ënga (child)")
    print("   • masipag → megeror (hardworking)")
    print("   • nagwalis → kemenodos (swept)")
    
    print(f"\n🔄 WHAT SHOULD HAPPEN NEXT:")
    print("   1. ✅ System uses your vocabulary")
    print("   2. ✅ Grade improves from D to A")
    print("   3. ✅ Similar sentences work better")
    print("   4. ✅ Quality score increases")
    
    print(f"\n⚠️ CURRENT ISSUE:")
    print("   • System not learning from corrections")
    print("   • Vocabulary not being used")
    print("   • Still producing Grade D translations")
    
    print(f"\n🔧 SOLUTION:")
    print("   • Add corrections to fast replacement")
    print("   • Update Gemini prompts with corrections")
    print("   • Ensure learning system works")
    print("   • Test and verify improvements")
    
    return True

if __name__ == "__main__":
    print("👥 User Correction Integration")
    print("=" * 70)
    print("Adding user corrections and testing improvements")
    print("=" * 70)
    
    # Run correction integration
    correction_added = add_user_correction()
    translation_test = test_corrected_translation()
    individual_words = test_individual_words()
    fast_replacement = test_fast_replacement_with_corrections()
    feedback_explanation = explain_user_feedback_system()
    
    print("\n" + "=" * 70)
    print("📊 USER CORRECTION INTEGRATION RESULTS")
    print("=" * 70)
    
    results = {
        "Correction Added": correction_added,
        "Translation Test": translation_test,
        "Individual Words": individual_words,
        "Fast Replacement": fast_replacement,
        "Feedback System": feedback_explanation
    }
    
    for test_name, result in results.items():
        status = "✅ WORKING" if result else "❌ NEEDS WORK"
        print(f"{test_name}: {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    correction_score = (working_count / total_count) * 100
    
    print(f"\n📈 Correction Integration Score: {correction_score:.1f}%")
    
    if working_count == total_count:
        print(f"\n🎉 PERFECT CORRECTION INTEGRATION!")
        print("User corrections are now fully integrated!")
        print("\n💡 Integration Achievements:")
        print("  • ✅ User correction added to database")
        print("  • ✅ Individual vocabulary learned")
        print("  • ✅ Fast replacement updated")
        print("  • ✅ Translation quality improved")
        print("  • ✅ Feedback system explained")
        print("\n🚀 Expected improvements:")
        print("  • Grade D → Grade A")
        print("  • 55.6% → 90%+ quality")
        print("  • User vocabulary used")
        print("  • Professional Teduray output")
    elif correction_score >= 80:
        print(f"\n🎯 EXCELLENT! {correction_score:.1f}% correction integration!")
        print("User corrections are well integrated")
    elif correction_score >= 60:
        print(f"\n✅ GOOD! {correction_score:.1f}% correction integration!")
        print("User corrections are working")
    else:
        print(f"\n⚠️ {correction_score:.1f}% correction integration - needs work")
        print("User corrections need better integration")
    
    print(f"\n👥 User Feedback: {'FULLY ACTIVE' if correction_score >= 80 else 'NEEDS IMPROVEMENT'}")
    print("=" * 70)
