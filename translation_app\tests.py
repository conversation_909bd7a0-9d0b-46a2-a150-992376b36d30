from django.test import TestCase
from .services import TranslationService

# Create your tests here.

class BibleVerseFetchTest(TestCase):
    def test_fetch_teduray_bible_verse(self):
        service = TranslationService()
        # Acts 1:12 is available in Teduray (TIY) on Bible.com
        teduray_verse = service._fetch_bible_verse('ACT', 1, 12, 'teduray')
        self.assertIsNotNone(teduray_verse)
        self.assertTrue(len(teduray_verse) > 0)
        self.assertNotIn('Tagalog', teduray_verse)
        print('Teduray verse:', teduray_verse)

    def test_fetch_tagalog_bible_verse(self):
        service = TranslationService()
        tagalog_verse = service._fetch_bible_verse('ACT', 1, 12, 'tagalog')
        self.assertIsNotNone(tagalog_verse)
        self.assertTrue(len(tagalog_verse) > 0)
        print('Tagalog verse:', tagalog_verse)

    def test_fetch_english_bible_verse(self):
        service = TranslationService()
        english_verse = service._fetch_bible_verse('ACT', 1, 12, 'english')
        self.assertIsNotNone(english_verse)
        self.assertTrue(len(english_verse) > 0)
        print('English verse:', english_verse)

class BibleTranslationLabelTest(TestCase):
    def test_biblecom_labeling(self):
        service = TranslationService()
        # Simulate a Bible-related Tagalog sentence
        text = "Mula sa bundok ng mga Olibo bumalik ang mga apostol sa Jerusalem."
        result = service.translate(text, 'tagalog', 'teduray')
        print('Translation result:', result)
        # Should be labeled as [Bible.com Teduray] or [Bible.com AI-Teduray] if a Bible.com verse is found, otherwise allow fallback
        self.assertTrue(result.startswith('[Bible.com') or '[No translation available]' in result or 'web search' in result or 'Gemini' in result)

class TedurayBibleCorpusTest(TestCase):
    def test_teduray_bible_corpus_lookup(self):
        service = TranslationService()
        # Acts 1:12 should be present in the local Teduray Bible corpus if extraction succeeded
        verse = service._lookup_teduray_bible_corpus('ACT', 1, 12)
        self.assertIsNotNone(verse)
        self.assertTrue(len(verse) > 0)
        print('Teduray Bible Corpus Acts 1:12:', verse)

    def test_translate_returns_teduray_bible_corpus(self):
        service = TranslationService()
        # This Tagalog sentence should match Acts 1:12 and return the Teduray Bible Corpus label
        text = "Mula sa bundok ng mga Olibo bumalik ang mga apostol sa Jerusalem."
        result = service.translate(text, 'tagalog', 'teduray')
        print('Translation result:', result)
        self.assertTrue(result.startswith('[Teduray Bible Corpus]') or '[No translation available]' in result)
