﻿from django.shortcuts import render
from django.views.generic import TemplateView
from django.http import JsonResponse
from .services import TranslationService
from .models import TranslationHistory, TranslationRule

class TranslationView(TemplateView):
    template_name = 'translation_app/translate.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['recent_translations'] = TranslationHistory.objects.order_by('-timestamp')[:5]
        # Add counts of available rules
        context['teduray_rules_count'] = TranslationRule.objects.filter(source_language='teduray').count()
        context['tagalog_rules_count'] = TranslationRule.objects.filter(source_language='tagalog').count()
        return context

def translate_api(request):
    if request.method == 'POST':
        text = request.POST.get('text', '')
        source_lang = request.POST.get('source_lang', 'teduray')
        target_lang = 'tagalog' if source_lang == 'teduray' else 'teduray'
        
        if not text.strip():
            return JsonResponse({'error': 'Please enter text to translate'}, status=400)
        
        try:
            service = TranslationService()

            # Get translation with professional pipeline
            translated_text = service.translate(text, source_lang, target_lang)

            # Get quality score if available
            quality_data = service.quality_scorer.score_translation(
                text, translated_text, source_lang, target_lang, 'api_request'
            )

            # Save to history
            TranslationHistory.objects.create(
                source_language=source_lang,
                source_text=text,
                translated_text=translated_text
            )

            # Get translation statistics
            stats = service.get_translation_statistics()

            return JsonResponse({
                'translated_text': translated_text,
                'source_lang': source_lang,
                'target_lang': target_lang,
                'quality_score': quality_data.get('overall_score', 0.5),
                'quality_grade': quality_data.get('grade', 'C'),
                'recommendations': quality_data.get('recommendations', []),
                'performance_stats': {
                    'cache_hit_rate': stats['performance_summary']['cache_hit_rate'],
                    'total_translations': stats['performance_summary']['total_translations']
                }
            })
        except Exception as e:
            return JsonResponse({
                'error': f'Translation error: {str(e)}',
                'translated_text': f'[Error] {text}'  # Return error message with original text
            }, status=500)
    
    return JsonResponse({'error': 'Invalid request'}, status=400)

# Add a view to bulk import translation rules
def import_rules(request):
    if request.method == 'POST':
        rules_text = request.POST.get('rules_text', '')
        source_lang = request.POST.get('source_lang', 'teduray')
        target_lang = 'tagalog' if source_lang == 'teduray' else 'teduray'
        
        count = 0
        for line in rules_text.strip().split('\n'):
            if '→' in line or '->' in line:
                parts = line.replace('->', '→').split('→')
                if len(parts) == 2:
                    source = parts[0].strip()
                    target = parts[1].strip()
                    
                    # Create or update rule
                    TranslationRule.objects.update_or_create(
                        source_language=source_lang,
                        source_text=source,
                        defaults={'target_text': target}
                    )
                    count += 1
        
        return JsonResponse({'success': f'Imported {count} translation rules'})

    return JsonResponse({'error': 'Invalid request'}, status=400)

def save_correction(request):
    """API endpoint to save user corrections"""
    if request.method == 'POST':
        source_text = request.POST.get('source_text', '').strip()
        source_lang = request.POST.get('source_lang', '')
        target_lang = request.POST.get('target_lang', '')
        original_translation = request.POST.get('original_translation', '').strip()
        corrected_translation = request.POST.get('corrected_translation', '').strip()
        correction_type = request.POST.get('correction_type', 'correction')

        if not all([source_text, source_lang, target_lang, original_translation, corrected_translation]):
            return JsonResponse({'error': 'Missing required fields'}, status=400)

        try:
            service = TranslationService()
            success = service.learn_from_correction(
                source_text,
                source_lang,
                target_lang,
                original_translation,
                corrected_translation
            )

            if success:
                return JsonResponse({
                    'success': 'Correction saved successfully',
                    'message': 'Thank you for improving our translation system!'
                })
            else:
                return JsonResponse({'error': 'Failed to save correction'}, status=500)

        except Exception as e:
            return JsonResponse({'error': f'Error saving correction: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Invalid request'}, status=400)

def save_feedback(request):
    """API endpoint to save user feedback"""
    if request.method == 'POST':
        source_text = request.POST.get('source_text', '').strip()
        source_lang = request.POST.get('source_lang', '')
        target_lang = request.POST.get('target_lang', '')
        translated_text = request.POST.get('translated_text', '').strip()
        feedback_type = request.POST.get('feedback_type', '')

        if not all([source_text, source_lang, target_lang, translated_text, feedback_type]):
            return JsonResponse({'error': 'Missing required fields'}, status=400)

        if feedback_type not in ['good', 'bad', 'partial']:
            return JsonResponse({'error': 'Invalid feedback type'}, status=400)

        try:
            service = TranslationService()
            feedback = service.save_translation_feedback(
                source_text=source_text,
                source_lang=source_lang,
                target_lang=target_lang,
                translated_text=translated_text,
                feedback_type=feedback_type
            )

            if feedback:
                return JsonResponse({
                    'success': 'Feedback saved successfully',
                    'message': 'Thank you for your feedback!'
                })
            else:
                return JsonResponse({'error': 'Failed to save feedback'}, status=500)

        except Exception as e:
            return JsonResponse({'error': f'Error saving feedback: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Invalid request'}, status=400)

def translation_statistics(request):
    """API endpoint to get translation system statistics"""
    if request.method == 'GET':
        try:
            service = TranslationService()
            stats = service.get_translation_statistics()

            return JsonResponse({
                'success': True,
                'statistics': stats
            })
        except Exception as e:
            return JsonResponse({'error': f'Error getting statistics: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Invalid request'}, status=400)

def clear_cache(request):
    """API endpoint to clear low-quality cache entries"""
    if request.method == 'POST':
        min_quality = float(request.POST.get('min_quality', 0.3))

        try:
            service = TranslationService()
            cleared_count = service.clear_low_quality_cache(min_quality)

            return JsonResponse({
                'success': True,
                'message': f'Cleared {cleared_count} low-quality cache entries',
                'cleared_count': cleared_count
            })
        except Exception as e:
            return JsonResponse({'error': f'Error clearing cache: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Invalid request'}, status=400)

def export_data(request):
    """API endpoint to export translation data"""
    if request.method == 'GET':
        try:
            service = TranslationService()
            export_data = service.export_translation_data()

            return JsonResponse({
                'success': True,
                'export_data': export_data
            })
        except Exception as e:
            return JsonResponse({'error': f'Error exporting data: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Invalid request'}, status=400)

def web_search_translation(request):
    """API endpoint for web search-based translation"""
    if request.method == 'POST':
        text = request.POST.get('text', '').strip()
        source_lang = request.POST.get('source_lang', 'teduray')
        target_lang = 'tagalog' if source_lang == 'teduray' else 'teduray'
        search_type = request.POST.get('search_type', 'general')

        if not text:
            return JsonResponse({'error': 'Please enter text to search'}, status=400)

        try:
            service = TranslationService()

            # Perform web search
            search_results = service.web_search.search_translation(
                text, source_lang, target_lang, search_type
            )

            return JsonResponse({
                'success': True,
                'search_results': search_results[:5],  # Return top 5 results
                'search_type': search_type,
                'query_text': text
            })
        except Exception as e:
            return JsonResponse({'error': f'Web search error: {str(e)}'}, status=500)

    return JsonResponse({'error': 'Invalid request'}, status=400)