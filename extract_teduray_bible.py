import re
import csv
from pathlib import Path
from pdfminer.high_level import extract_text

PDF_PATH = "https://www.scriptureearth.org/data/tiy/PDF/tiyNT-web.pdf"
OUTPUT_CSV = "teduray_bible_verses.csv"

# Download PDF if not present
def download_pdf(url, dest):
    import requests
    if Path(dest).exists():
        print(f"PDF already downloaded: {dest}")
        return
    print(f"Downloading {url} ...")
    r = requests.get(url)
    with open(dest, "wb") as f:
        f.write(r.content)
    print(f"Saved to {dest}")

def extract_verses_from_pdf(pdf_path):
    print(f"Extracting text from {pdf_path} ...")
    text = extract_text(pdf_path)
    lines = text.splitlines()
    verses = []
    current_book = None
    current_chapter = None
    current_verse = None
    current_text = []
    # Patterns for book/chapter/verse
    book_chapter_pat = re.compile(r"^(<PERSON>|<PERSON>|<PERSON>|<PERSON>|<PERSON>awa|Roma|1 Corinto|2 Corinto|Galacia|Efeso|Filipos|Colosas|1 Tesalonica|2 Tesalonica|1 Timoteo|2 Timoteo|Tito|Filemon|Hebreo|Santiago|1 Pedro|2 Pedro|1 Juan|2 <PERSON>|3 <PERSON>|Judas|Pahayag)\s*(\d+)?")
    verse_pat = re.compile(r"^(\d{1,3})\s{0,1}[\u2009\u200A\u200B\s]?(.*)$")  # e.g. '3 Atin ...'
    for line in lines:
        line = line.strip()
        if not line:
            continue
        # Detect book/chapter
        m_book = book_chapter_pat.match(line)
        if m_book:
            if current_verse and current_text:
                verses.append({
                    "book": current_book,
                    "chapter": current_chapter,
                    "verse": current_verse,
                    "teduray_text": " ".join(current_text).strip()
                })
            current_book = m_book.group(1)
            if m_book.group(2):
                current_chapter = m_book.group(2)
            current_verse = None
            current_text = []
            continue
        # Detect verse number
        m_verse = verse_pat.match(line)
        if m_verse and m_verse.group(1) and m_verse.group(2):
            # Save previous verse
            if current_verse and current_text:
                verses.append({
                    "book": current_book,
                    "chapter": current_chapter,
                    "verse": current_verse,
                    "teduray_text": " ".join(current_text).strip()
                })
            current_verse = m_verse.group(1)
            current_text = [m_verse.group(2)]
        elif current_verse:
            # Accumulate text for current verse
            current_text.append(line)
    # Save last verse
    if current_verse and current_text:
        verses.append({
            "book": current_book,
            "chapter": current_chapter,
            "verse": current_verse,
            "teduray_text": " ".join(current_text).strip()
        })
    print(f"Extracted {len(verses)} verses.")
    return verses

def save_verses_to_csv(verses, csv_path):
    with open(csv_path, "w", encoding="utf-8", newline="") as f:
        writer = csv.DictWriter(f, fieldnames=["book", "chapter", "verse", "teduray_text"])
        writer.writeheader()
        for v in verses:
            writer.writerow(v)
    print(f"Saved {len(verses)} verses to {csv_path}")

def main():
    local_pdf = "tiyNT-web.pdf"
    download_pdf(PDF_PATH, local_pdf)
    verses = extract_verses_from_pdf(local_pdf)
    save_verses_to_csv(verses, OUTPUT_CSV)

if __name__ == "__main__":
    main()
