#!/usr/bin/env python
"""
Test the native speaker vocabulary integration
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_native_speaker_vocabulary():
    """Test the new native speaker vocabulary"""
    print("🎯 Testing Native Speaker Vocabulary")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test the exact native speaker translations
        native_examples = [
            {
                'input': "Panaginip lang pala.",
                'expected': "Tëginëf sa wayo.",
                'description': "Native speaker example 1"
            },
            {
                'input': "Hindi ko alam na nakainom na pala siya.",
                'expected': "Ënda mënggëtëwa ku dek mënggëinëm wayo.",
                'description': "Native speaker example 2"
            }
        ]
        
        print("🔍 Testing native speaker examples:")
        
        successful_tests = 0
        for example in native_examples:
            input_text = example['input']
            expected = example['expected']
            description = example['description']
            
            print(f"\n📝 {description}")
            print(f"   Input: '{input_text}'")
            print(f"   Expected: '{expected}'")
            
            result = service.translate(input_text, "tagalog", "teduray")
            print(f"   Got: '{result}'")
            
            # Check for key vocabulary usage
            key_words_found = 0
            total_key_words = 0
            
            if 'panaginip' in input_text.lower():
                total_key_words += 1
                if 'tëginëf' in result.lower():
                    key_words_found += 1
                    print(f"     ✅ panaginip → tëginëf")
                else:
                    print(f"     ❌ panaginip not translated to tëginëf")
            
            if 'lang' in input_text.lower():
                total_key_words += 1
                if 'sa' in result.lower():
                    key_words_found += 1
                    print(f"     ✅ lang → sa")
                else:
                    print(f"     ❌ lang not translated to sa")
            
            if 'alam' in input_text.lower():
                total_key_words += 1
                if 'gëtëwa' in result.lower():
                    key_words_found += 1
                    print(f"     ✅ alam → gëtëwa")
                else:
                    print(f"     ❌ alam not translated to gëtëwa")
            
            if 'nakainom' in input_text.lower():
                total_key_words += 1
                if 'mënggëinëm' in result.lower():
                    key_words_found += 1
                    print(f"     ✅ nakainom → mënggëinëm")
                else:
                    print(f"     ❌ nakainom not translated to mënggëinëm")
            
            if total_key_words > 0:
                success_ratio = key_words_found / total_key_words
                print(f"   📊 Key vocabulary success: {success_ratio:.1%} ({key_words_found}/{total_key_words})")
                
                if success_ratio >= 0.7:
                    successful_tests += 1
                elif success_ratio >= 0.5:
                    successful_tests += 0.5
            else:
                successful_tests += 0.5  # No key words to test
        
        overall_success = successful_tests / len(native_examples)
        print(f"\n📊 Overall native speaker vocabulary success: {overall_success:.1%}")
        
        if overall_success >= 0.8:
            print("🎉 EXCELLENT! Native speaker vocabulary working great!")
            return True
        elif overall_success >= 0.6:
            print("✅ GOOD! Native speaker vocabulary mostly working!")
            return True
        else:
            print("⚠️ Native speaker vocabulary needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Native speaker vocabulary test error: {e}")
        return False

def test_new_vocabulary_in_sentences():
    """Test new vocabulary in different sentence contexts"""
    print("\n📝 Testing New Vocabulary in Sentences")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test sentences using the new vocabulary
        test_sentences = [
            {
                'input': "Panaginip ko lang ito.",
                'expected_words': ['panaginip→tëginëf', 'lang→sa'],
                'description': "Dream sentence with 'lang'"
            },
            {
                'input': "Hindi ko alam ang panaginip mo.",
                'expected_words': ['alam→gëtëwa', 'panaginip→tëginëf'],
                'description': "Knowledge and dream context"
            },
            {
                'input': "Nakainom lang siya ng tubig.",
                'expected_words': ['nakainom→mënggëinëm', 'lang→sa'],
                'description': "Drinking context with 'lang'"
            },
            {
                'input': "Alam mo ba na panaginip lang ito?",
                'expected_words': ['alam→gëtëwa', 'panaginip→tëginëf', 'lang→sa'],
                'description': "Question with multiple new words"
            }
        ]
        
        print("🔍 Testing new vocabulary in sentences:")
        
        successful_sentences = 0
        for sentence in test_sentences:
            input_text = sentence['input']
            expected_words = sentence['expected_words']
            description = sentence['description']
            
            print(f"\n📝 {description}")
            print(f"   Input: '{input_text}'")
            print(f"   Expected: {expected_words}")
            
            result = service.translate(input_text, "tagalog", "teduray")
            print(f"   Result: '{result}'")
            
            # Check for expected vocabulary
            words_found = 0
            for expected in expected_words:
                if '→' in expected:
                    old_word, new_word = expected.split('→')
                    if new_word.lower() in result.lower():
                        print(f"     ✅ {expected}")
                        words_found += 1
                    else:
                        print(f"     ❌ {expected}")
            
            success_ratio = words_found / len(expected_words)
            print(f"   📊 Vocabulary success: {success_ratio:.1%}")
            
            if success_ratio >= 0.7:
                successful_sentences += 1
            elif success_ratio >= 0.5:
                successful_sentences += 0.5
        
        overall_success = successful_sentences / len(test_sentences)
        print(f"\n📊 Overall sentence vocabulary success: {overall_success:.1%}")
        
        if overall_success >= 0.7:
            print("🎉 EXCELLENT! New vocabulary working in sentences!")
            return True
        elif overall_success >= 0.5:
            print("✅ GOOD! New vocabulary mostly working in sentences!")
            return True
        else:
            print("⚠️ New vocabulary in sentences needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Sentence vocabulary test error: {e}")
        return False

def test_fast_replacement_with_new_words():
    """Test fast replacement with new vocabulary"""
    print("\n⚡ Testing Fast Replacement with New Words")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test individual words and phrases
        test_cases = [
            {
                'input': "panaginip",
                'expected': "tëginëf",
                'description': "Single word: dream"
            },
            {
                'input': "lang",
                'expected': "sa",
                'description': "Single word: just/only"
            },
            {
                'input': "alam",
                'expected': "gëtëwa",
                'description': "Single word: know"
            },
            {
                'input': "nakainom",
                'expected': "mënggëinëm",
                'description': "Single word: drank"
            },
            {
                'input': "panaginip lang",
                'expected_contains': ["tëginëf", "sa"],
                'description': "Two word phrase"
            },
            {
                'input': "hindi ko alam",
                'expected_contains': ["ënda", "gëtëwa"],
                'description': "Three word phrase"
            }
        ]
        
        print("🔍 Testing fast replacement with new words:")
        
        successful_tests = 0
        for test_case in test_cases:
            input_text = test_case['input']
            description = test_case['description']
            
            print(f"\n📝 {description}")
            print(f"   Input: '{input_text}'")
            
            # Test fast replacement directly
            result = service._fast_authentic_word_replacement(input_text, "tagalog", "teduray")
            
            if result:
                print(f"   Result: '{result}'")
                
                # Check expectations
                if 'expected' in test_case:
                    expected = test_case['expected']
                    if expected.lower() in result.lower():
                        print(f"     ✅ Contains expected: {expected}")
                        successful_tests += 1
                    else:
                        print(f"     ❌ Missing expected: {expected}")
                
                elif 'expected_contains' in test_case:
                    expected_words = test_case['expected_contains']
                    found_words = 0
                    for word in expected_words:
                        if word.lower() in result.lower():
                            print(f"     ✅ Contains: {word}")
                            found_words += 1
                        else:
                            print(f"     ❌ Missing: {word}")
                    
                    if found_words >= len(expected_words) * 0.7:
                        successful_tests += 1
                    elif found_words > 0:
                        successful_tests += 0.5
            else:
                print(f"   ❌ No result from fast replacement")
        
        success_rate = successful_tests / len(test_cases)
        print(f"\n📊 Fast replacement success: {success_rate:.1%}")
        
        if success_rate >= 0.8:
            print("🎉 EXCELLENT! Fast replacement with new words working!")
            return True
        elif success_rate >= 0.6:
            print("✅ GOOD! Fast replacement with new words mostly working!")
            return True
        else:
            print("⚠️ Fast replacement with new words needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Fast replacement test error: {e}")
        return False

def test_overall_improvement():
    """Test overall improvement with native speaker data"""
    print("\n📊 Testing Overall Improvement")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test sentences that should show improvement
        improvement_tests = [
            {
                'sentence': "Panaginip lang pala.",
                'before_quality': 30,  # Estimated before
                'description': "Native speaker example 1"
            },
            {
                'sentence': "Hindi ko alam na nakainom na pala siya.",
                'before_quality': 40,  # Estimated before
                'description': "Native speaker example 2"
            },
            {
                'sentence': "Panaginip ko lang ito.",
                'before_quality': 35,  # Estimated before
                'description': "Dream-related sentence"
            },
            {
                'sentence': "Hindi ko alam ang panaginip mo.",
                'before_quality': 45,  # Estimated before
                'description': "Knowledge and dream context"
            }
        ]
        
        print("🔍 Testing overall improvement:")
        
        total_improvement = 0
        for test in improvement_tests:
            sentence = test['sentence']
            before_quality = test['before_quality']
            description = test['description']
            
            print(f"\n📝 {description}")
            print(f"   Sentence: '{sentence}'")
            print(f"   Estimated before: {before_quality}%")
            
            result = service.translate(sentence, "tagalog", "teduray")
            print(f"   Result: '{result}'")
            
            # Estimate current quality based on translation
            original_words = sentence.split()
            result_words = result.split()
            
            translated_count = 0
            for orig, trans in zip(original_words, result_words):
                if orig.lower() != trans.lower():
                    translated_count += 1
            
            current_quality = (translated_count / len(original_words)) * 100
            improvement = current_quality - before_quality
            
            print(f"   Current quality: {current_quality:.1f}%")
            print(f"   Improvement: +{improvement:.1f}%")
            
            total_improvement += improvement
        
        average_improvement = total_improvement / len(improvement_tests)
        print(f"\n📊 Average improvement: +{average_improvement:.1f}%")
        
        if average_improvement >= 30:
            print("🎉 EXCELLENT! Significant improvement achieved!")
            return True
        elif average_improvement >= 20:
            print("✅ GOOD! Notable improvement achieved!")
            return True
        elif average_improvement >= 10:
            print("⚠️ MODERATE improvement achieved")
            return True
        else:
            print("❌ Limited improvement detected")
            return False
            
    except Exception as e:
        print(f"❌ Overall improvement test error: {e}")
        return False

if __name__ == "__main__":
    print("🎯 Native Speaker Integration Test")
    print("=" * 70)
    print("Testing integration of native speaker vocabulary")
    print("=" * 70)
    
    # Run integration tests
    vocabulary_test = test_native_speaker_vocabulary()
    sentence_test = test_new_vocabulary_in_sentences()
    fast_replacement_test = test_fast_replacement_with_new_words()
    improvement_test = test_overall_improvement()
    
    print("\n" + "=" * 70)
    print("📊 NATIVE SPEAKER INTEGRATION RESULTS")
    print("=" * 70)
    
    results = {
        "Native Speaker Vocabulary": vocabulary_test,
        "Vocabulary in Sentences": sentence_test,
        "Fast Replacement": fast_replacement_test,
        "Overall Improvement": improvement_test
    }
    
    for test_name, result in results.items():
        status = "✅ WORKING" if result else "❌ NEEDS WORK"
        print(f"{test_name}: {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    integration_score = (working_count / total_count) * 100
    
    print(f"\n📈 Integration Score: {integration_score:.1f}%")
    
    if working_count == total_count:
        print(f"\n🎉 PERFECT INTEGRATION!")
        print("Native speaker vocabulary is fully integrated!")
        print("\n💡 Integration Achievements:")
        print("  • ✅ Native speaker vocabulary working")
        print("  • ✅ New words working in sentences")
        print("  • ✅ Fast replacement updated")
        print("  • ✅ Overall quality improved")
        print("\n🚀 Your system now has:")
        print("  • Authentic dream vocabulary (panaginip→tëginëf)")
        print("  • Proper particle usage (lang→sa)")
        print("  • Knowledge expressions (alam→gëtëwa)")
        print("  • Drinking/consumption terms (nakainom→mënggëinëm)")
        print("  • Better temporal markers (na→dek)")
    elif integration_score >= 75:
        print(f"\n🎯 EXCELLENT! {integration_score:.1f}% integration achieved!")
        print("Native speaker vocabulary is well integrated")
    elif integration_score >= 50:
        print(f"\n✅ GOOD! {integration_score:.1f}% integration achieved!")
        print("Native speaker vocabulary is working")
    else:
        print(f"\n⚠️ {integration_score:.1f}% integration - needs more work")
        print("Native speaker vocabulary integration needs improvement")
    
    print(f"\n🎯 Native Speaker Vocabulary: {'FULLY ACTIVE' if integration_score >= 75 else 'PARTIALLY ACTIVE'}")
    print("=" * 70)
