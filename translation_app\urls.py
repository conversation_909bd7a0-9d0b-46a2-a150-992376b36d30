from django.urls import path
from . import views

urlpatterns = [
    path('', views.TranslationView.as_view(), name='translate'),
    path('api/translate/', views.translate_api, name='translate_api'),
    path('api/import-rules/', views.import_rules, name='import_rules'),
    path('api/save-correction/', views.save_correction, name='save_correction'),
    path('api/save-feedback/', views.save_feedback, name='save_feedback'),

    # Professional API endpoints
    path('api/statistics/', views.translation_statistics, name='translation_statistics'),
    path('api/clear-cache/', views.clear_cache, name='clear_cache'),
    path('api/export-data/', views.export_data, name='export_data'),
    path('api/web-search/', views.web_search_translation, name='web_search_translation'),
]