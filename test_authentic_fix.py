#!/usr/bin/env python
"""
Test the authentic patterns fix
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_authentic_patterns_in_database():
    """Test if authentic patterns are now in the database"""
    print("🔧 Testing Authentic Patterns in Database")
    print("=" * 50)
    
    try:
        from translation_app.models import TranslationRule
        
        # Check for key authentic patterns
        authentic_checks = [
            ("anak", "ënga"),
            ("bata", "ënga"),
            ("dalawa", "ruwo"),
            ("mayroon", "uwëni"),
            ("wala", "ënda"),
            ("oo", "oo"),
            ("hindi", "bëkën"),
            ("tayo", "dëmo"),
            ("bukas", "sëgito"),
        ]
        
        print("🔍 Checking authentic patterns in database:")
        
        found_count = 0
        for tagalog, expected_teduray in authentic_checks:
            rule = TranslationRule.objects.filter(
                source_language='tagalog',
                source_text__iexact=tagalog
            ).first()
            
            if rule:
                if expected_teduray in rule.target_text:
                    print(f"   ✅ '{tagalog}' → '{rule.target_text}' (contains '{expected_teduray}')")
                    found_count += 1
                else:
                    print(f"   ⚠️  '{tagalog}' → '{rule.target_text}' (expected '{expected_teduray}')")
            else:
                print(f"   ❌ '{tagalog}' not found in database")
        
        total_checks = len(authentic_checks)
        print(f"\n📊 Found {found_count}/{total_checks} authentic patterns")
        
        if found_count >= total_checks * 0.8:
            print("✅ Most authentic patterns are in database!")
            return True
        else:
            print("⚠️  Need to add more authentic patterns")
            return False
            
    except Exception as e:
        print(f"❌ Database check error: {e}")
        return False

def test_native_speaker_examples():
    """Test the exact native speaker examples"""
    print("\n🎯 Testing Native Speaker Examples")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Native speaker examples
        native_examples = [
            ("May mga anak ka ba?", "Uwënëni do ëngaëm?"),
            ("Mayroon akong dalawang anak.", "Uwëni ruwo gu ënga."),
            ("Wala akong anak.", "Ënda i ënga gu."),
            ("Oo, mahal ko ito!", "Oo, mëuyotub ni."),
            ("hindi ko alam.", "Ënda gëtuwa gu de."),
            ("Natatakot ka ba?", "Mëgilak go?"),
        ]
        
        print("🔍 Testing native speaker examples:")
        
        good_translations = 0
        for tagalog, expected in native_examples:
            print(f"\n📝 Input: '{tagalog}'")
            print(f"   Expected: '{expected}'")
            
            result = service.translate(tagalog, "tagalog", "teduray")
            print(f"   Got: '{result}'")
            
            # Check if result contains key authentic elements
            expected_words = expected.split()
            result_words = result.split()
            
            # Count how many expected words are in the result
            matches = 0
            for exp_word in expected_words:
                if any(exp_word.lower() in res_word.lower() for res_word in result_words):
                    matches += 1
            
            match_ratio = matches / len(expected_words) if expected_words else 0
            
            if match_ratio >= 0.5:
                print(f"   ✅ GOOD MATCH ({match_ratio:.1%} authentic elements)")
                good_translations += 1
            elif match_ratio >= 0.3:
                print(f"   ⚠️  PARTIAL MATCH ({match_ratio:.1%} authentic elements)")
                good_translations += 0.5
            else:
                print(f"   ❌ POOR MATCH ({match_ratio:.1%} authentic elements)")
        
        total_examples = len(native_examples)
        print(f"\n📊 Good translations: {good_translations}/{total_examples}")
        
        if good_translations >= total_examples * 0.7:
            print("✅ System is using authentic patterns!")
            return True
        else:
            print("⚠️  System needs more work on authentic patterns")
            return False
            
    except Exception as e:
        print(f"❌ Native speaker test error: {e}")
        return False

def test_word_corrections():
    """Test if individual words are corrected"""
    print("\n🔧 Testing Word Corrections")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Words that should be corrected
        word_tests = [
            ("anak", "ënga"),
            ("bata", "ënga"),
            ("dalawa", "ruwo"),
            ("mayroon", "uwëni"),
            ("wala", "ënda"),
            ("hindi", "bëkën"),
        ]
        
        print("🔍 Testing word corrections:")
        
        corrected_count = 0
        for tagalog, expected in word_tests:
            print(f"\n📝 Testing: '{tagalog}'")
            print(f"   Should be: '{expected}'")
            
            result = service.translate(tagalog, "tagalog", "teduray")
            print(f"   Got: '{result}'")
            
            if expected.lower() in result.lower():
                print(f"   ✅ CORRECTED!")
                corrected_count += 1
            elif result.lower() != tagalog.lower():
                print(f"   ⚠️  CHANGED (may be acceptable)")
                corrected_count += 0.5
            else:
                print(f"   ❌ NOT CORRECTED")
        
        total_words = len(word_tests)
        print(f"\n📊 Corrected words: {corrected_count}/{total_words}")
        
        if corrected_count >= total_words * 0.7:
            print("✅ Word corrections working!")
            return True
        else:
            print("⚠️  Word corrections need improvement")
            return False
            
    except Exception as e:
        print(f"❌ Word correction test error: {e}")
        return False

def test_system_initialization():
    """Test if the system initializes with authentic patterns"""
    print("\n🚀 Testing System Initialization")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        # Create a new service instance (should trigger pattern addition)
        print("🔄 Creating new TranslationService instance...")
        service = TranslationService()
        
        print("✅ Service initialized successfully")
        
        # Check if patterns were added during initialization
        from translation_app.models import TranslationRule
        
        pattern_count = TranslationRule.objects.filter(
            source_language='tagalog',
            source_text__in=['anak', 'bata', 'dalawa', 'mayroon', 'wala']
        ).count()
        
        print(f"📊 Found {pattern_count} key patterns in database")
        
        if pattern_count >= 3:
            print("✅ Initialization added authentic patterns!")
            return True
        else:
            print("⚠️  Initialization may not have added patterns")
            return False
            
    except Exception as e:
        print(f"❌ Initialization test error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Authentic Patterns Fix")
    print("=" * 60)
    print("Verifying that native speaker patterns are working")
    print("=" * 60)
    
    # Run tests
    database_ok = test_authentic_patterns_in_database()
    examples_ok = test_native_speaker_examples()
    corrections_ok = test_word_corrections()
    init_ok = test_system_initialization()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    results = {
        "Database Patterns": database_ok,
        "Native Speaker Examples": examples_ok,
        "Word Corrections": corrections_ok,
        "System Initialization": init_ok
    }
    
    for test_name, result in results.items():
        status = "✅ WORKING" if result else "❌ NEEDS WORK"
        print(f"{test_name}: {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    
    if working_count == total_count:
        print(f"\n🎉 ALL {total_count} SYSTEMS WORKING!")
        print("The system now uses authentic native speaker patterns!")
        print("\n💡 Key Improvements:")
        print("  • ✅ Authentic patterns added to database")
        print("  • ✅ Native speaker examples working")
        print("  • ✅ Word corrections implemented")
        print("  • ✅ System initializes with patterns")
        print("\n🎯 Your translations should now be much more authentic!")
    else:
        print(f"\n⚠️  {working_count}/{total_count} systems working")
        print("The system is improving but may need more refinement")
    
    print("\n🔧 The feedback endpoint should also work now!")
    print("=" * 60)
