﻿{% load static %}
<!DOCTYPE html>
<html>
<head>
    <title>Teduray-Tagalog Translator</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <style>
        .translation-box {
            min-height: 150px;
        }

        .nav-tabs {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">Teduray-Tagalog Translator</h1>

        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="translate-tab" data-bs-toggle="tab" data-bs-target="#translate" type="button" role="tab">Translate</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import" type="button" role="tab">Import Rules</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">History</button>
            </li>
        </ul>

        <div class="tab-content" id="myTabContent">
            <!-- Translation Tab -->
            <div class="tab-pane fade show active" id="translate" role="tabpanel">
                <div class="row">
                    <div class="col-md-5">
                        <div class="form-group">
                            <label>Source Language:</label>
                            <select id="source-lang" class="form-control">
                                <option value="teduray">Teduray ({{ teduray_rules_count }} rules)</option>
                                <option value="tagalog">Tagalog ({{ tagalog_rules_count }} rules)</option>
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <textarea id="source-text" class="form-control translation-box"
                                      placeholder="Enter text to translate"></textarea>
                        </div>
                        <button id="translate-btn" class="btn btn-primary mt-3">Translate</button>
                    </div>

                    <div class="col-md-2 d-flex align-items-center justify-content-center">
                        <div class="text-center">
                            <i class="bi bi-arrow-right" style="font-size: 2rem;">→</i>
                        </div>
                    </div>

                    <div class="col-md-5">
                        <div class="form-group">
                            <label>Target Language:</label>
                            <select id="target-lang" class="form-control" disabled>
                                <option value="tagalog">Tagalog</option>
                                <option value="teduray">Teduray</option>
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <textarea id="target-text" class="form-control translation-box"
                                      placeholder="Translation will appear here" readonly></textarea>
                        </div>

                        <!-- Translation feedback and correction section -->
                        <div id="feedback-section" class="mt-3" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Help improve our translation system</h6>
                                </div>
                                <div class="card-body">
                                    <!-- Feedback buttons -->
                                    <div class="mb-3">
                                        <label class="form-label">How was this translation?</label>
                                        <div class="btn-group d-block" role="group">
                                            <button type="button" class="btn btn-outline-success btn-sm feedback-btn" data-feedback="good">
                                                👍 Good
                                            </button>
                                            <button type="button" class="btn btn-outline-warning btn-sm feedback-btn" data-feedback="partial">
                                                🤔 Partially Correct
                                            </button>
                                            <button type="button" class="btn btn-outline-danger btn-sm feedback-btn" data-feedback="bad">
                                                👎 Incorrect
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Correction input -->
                                    <div id="correction-input" style="display: none;">
                                        <label class="form-label">Provide the correct translation:</label>
                                        <div class="input-group">
                                            <textarea id="corrected-text" class="form-control" rows="2"
                                                      placeholder="Enter the correct translation here"></textarea>
                                            <button class="btn btn-primary" type="button" id="submit-correction">
                                                Submit Correction
                                            </button>
                                        </div>
                                        <small class="text-muted">Your correction will help improve future translations.</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Import Rules Tab -->
            <div class="tab-pane fade" id="import" role="tabpanel">
                <div class="row">
                    <div class="col-md-12">
                        <h3>Import Translation Rules</h3>
                        <p>Enter translation rules in the format: <code>source_word → target_word</code>, one per line.</p>

                        <div class="form-group">
                            <label>Source Language:</label>
                            <select id="import-source-lang" class="form-control">
                                <option value="teduray">Teduray</option>
                                <option value="tagalog">Tagalog</option>
                            </select>
                        </div>

                        <div class="form-group mt-2">
                            <textarea id="rules-text" class="form-control" rows="10"
                                      placeholder="Example:
hello → kumusta
goodbye → paalam"></textarea>
                        </div>

                        <button id="import-btn" class="btn btn-success mt-3">Import Rules</button>
                        <div id="import-result" class="alert mt-3" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- History Tab -->
            <div class="tab-pane fade" id="history" role="tabpanel">
                <h3>Recent Translations</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Source Language</th>
                                <th>Source Text</th>
                                <th>Translated Text</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for translation in recent_translations %}
                            <tr>
                                <td>{{ translation.source_language|title }}</td>
                                <td>{{ translation.source_text }}</td>
                                <td>{{ translation.translated_text }}</td>
                                <td>{{ translation.timestamp|date:"M d, Y H:i" }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center">No translation history yet</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Update target language when source language changes
            $('#source-lang').change(function() {
                if ($(this).val() === 'teduray') {
                    $('#target-lang').val('tagalog');
                } else {
                    $('#target-lang').val('teduray');
                }
            });

            // Handle translation
            $('#translate-btn').click(function() {
                const sourceText = $('#source-text').val();
                const sourceLang = $('#source-lang').val();

                if (!sourceText) return;

                // Show loading state
                $('#translate-btn').prop('disabled', true).text('Translating...');

                $.ajax({
                    url: '{% url "translate_api" %}',
                    type: 'POST',
                    data: {
                        text: sourceText,
                        source_lang: sourceLang,
                        csrfmiddlewaretoken: '{{ csrf_token }}'
                    },
                    success: function(response) {
                        $('#target-text').val(response.translated_text);
                        // Show feedback section after successful translation
                        $('#feedback-section').show();
                        // Reset feedback buttons and correction input
                        $('.feedback-btn').removeClass('btn-success btn-warning btn-danger').addClass('btn-outline-success btn-outline-warning btn-outline-danger');
                        $('#correction-input').hide();
                        $('#corrected-text').val('');
                    },
                    error: function(xhr) {
                        let errorMsg = 'Translation failed. Please try again.';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMsg = xhr.responseJSON.error;
                        }
                        alert(errorMsg);

                        if (xhr.responseJSON && xhr.responseJSON.translated_text) {
                            $('#target-text').val(xhr.responseJSON.translated_text);
                        }
                    },
                    complete: function() {
                        $('#translate-btn').prop('disabled', false).text('Translate');
                    }
                });
            });

            // Handle rule import
            $('#import-btn').click(function() {
                const rulesText = $('#rules-text').val();
                const sourceLang = $('#import-source-lang').val();

                if (!rulesText) return;

                // Show loading state
                $('#import-btn').prop('disabled', true).text('Importing...');

                $.ajax({
                    url: '{% url "import_rules" %}',
                    type: 'POST',
                    data: {
                        rules_text: rulesText,
                        source_lang: sourceLang,
                        csrfmiddlewaretoken: '{{ csrf_token }}'
                    },
                    success: function(response) {
                        $('#import-result')
                            .removeClass('alert-danger')
                            .addClass('alert-success')
                            .text(response.success)
                            .show();

                        // Clear the textarea
                        $('#rules-text').val('');

                        // Reload page after 2 seconds to update rule counts
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    },
                    error: function(xhr) {
                        let errorMsg = 'Import failed. Please try again.';
                        if (xhr.responseJSON && xhr.responseJSON.error) {
                            errorMsg = xhr.responseJSON.error;
                        }

                        $('#import-result')
                            .removeClass('alert-success')
                            .addClass('alert-danger')
                            .text(errorMsg)
                            .show();
                    },
                    complete: function() {
                        $('#import-btn').prop('disabled', false).text('Import Rules');
                    }
                });
            });

            // Handle feedback buttons
            $('.feedback-btn').click(function() {
                const feedbackType = $(this).data('feedback');
                const sourceText = $('#source-text').val();
                const sourceLang = $('#source-lang').val();
                const targetLang = $('#target-lang').val();
                const translatedText = $('#target-text').val();

                if (!sourceText || !translatedText) return;

                // Update button appearance
                $('.feedback-btn').removeClass('btn-success btn-warning btn-danger').addClass('btn-outline-success btn-outline-warning btn-outline-danger');
                $(this).removeClass('btn-outline-success btn-outline-warning btn-outline-danger');

                if (feedbackType === 'good') {
                    $(this).addClass('btn-success');
                } else if (feedbackType === 'partial') {
                    $(this).addClass('btn-warning');
                } else {
                    $(this).addClass('btn-danger');
                }

                // Show correction input for bad or partial feedback
                if (feedbackType === 'bad' || feedbackType === 'partial') {
                    $('#correction-input').show();
                } else {
                    $('#correction-input').hide();
                }

                // Save feedback
                $.ajax({
                    url: '{% url "save_feedback" %}',
                    type: 'POST',
                    data: {
                        source_text: sourceText,
                        source_lang: sourceLang,
                        target_lang: targetLang,
                        translated_text: translatedText,
                        feedback_type: feedbackType,
                        csrfmiddlewaretoken: '{{ csrf_token }}'
                    },
                    success: function(response) {
                        console.log('Feedback saved:', response.message);
                    },
                    error: function(xhr) {
                        console.error('Failed to save feedback:', xhr.responseJSON?.error);
                    }
                });
            });

            // Handle correction submission
            $('#submit-correction').click(function() {
                const sourceText = $('#source-text').val();
                const sourceLang = $('#source-lang').val();
                const targetLang = $('#target-lang').val();
                const originalTranslation = $('#target-text').val();
                const correctedTranslation = $('#corrected-text').val().trim();

                if (!correctedTranslation) {
                    alert('Please enter a correction.');
                    return;
                }

                // Show loading state
                $(this).prop('disabled', true).text('Submitting...');

                $.ajax({
                    url: '{% url "save_correction" %}',
                    type: 'POST',
                    data: {
                        source_text: sourceText,
                        source_lang: sourceLang,
                        target_lang: targetLang,
                        original_translation: originalTranslation,
                        corrected_translation: correctedTranslation,
                        correction_type: 'correction',
                        csrfmiddlewaretoken: '{{ csrf_token }}'
                    },
                    success: function(response) {
                        alert(response.message || 'Correction saved successfully!');
                        $('#corrected-text').val('');
                        $('#correction-input').hide();

                        // Update the translation with the corrected version
                        $('#target-text').val(correctedTranslation);
                    },
                    error: function(xhr) {
                        alert('Failed to save correction: ' + (xhr.responseJSON?.error || 'Unknown error'));
                    },
                    complete: function() {
                        $('#submit-correction').prop('disabled', false).text('Submit Correction');
                    }
                });
            });
        });
    </script>
</body>
</html>