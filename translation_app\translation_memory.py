"""
Professional Translation Memory System
Provides intelligent caching, learning, and quality assessment for translations.
"""

import json
import hashlib
from datetime import datetime, timedelta
from django.core.cache import cache
from django.db.models import Q, Avg
from .models import TranslationRule, TranslationHistory, TranslationCorrection, TranslationFeedback


class TranslationMemory:
    """Advanced translation memory with intelligent caching and learning capabilities"""
    
    def __init__(self):
        self.cache_timeout = 3600 * 24  # 24 hours
        self.quality_threshold = 0.7  # Minimum quality score for auto-acceptance
        
    def get_cache_key(self, text, source_lang, target_lang):
        """Generate a unique cache key for translation requests"""
        content = f"{text}:{source_lang}:{target_lang}"
        return f"translation:{hashlib.md5(content.encode()).hexdigest()}"
    
    def get_cached_translation(self, text, source_lang, target_lang):
        """Retrieve cached translation if available and still valid"""
        cache_key = self.get_cache_key(text, source_lang, target_lang)
        cached_data = cache.get(cache_key)
        
        if cached_data:
            # Check if cached translation has good quality score
            if cached_data.get('quality_score', 0) >= self.quality_threshold:
                return cached_data['translation']
        
        return None
    
    def cache_translation(self, text, source_lang, target_lang, translation, quality_score=None, source="unknown"):
        """Cache a translation with metadata"""
        cache_key = self.get_cache_key(text, source_lang, target_lang)
        
        cache_data = {
            'translation': translation,
            'quality_score': quality_score or 0.5,
            'source': source,
            'timestamp': datetime.now().isoformat(),
            'usage_count': 1
        }
        
        # Update usage count if already cached
        existing = cache.get(cache_key)
        if existing:
            cache_data['usage_count'] = existing.get('usage_count', 0) + 1
        
        cache.set(cache_key, cache_data, self.cache_timeout)
        
        # Also save to database for permanent storage
        self._save_to_database(text, source_lang, target_lang, translation, quality_score, source)
    
    def _save_to_database(self, text, source_lang, target_lang, translation, quality_score, source):
        """Save translation to database for permanent memory"""
        try:
            # Save to translation history
            TranslationHistory.objects.create(
                source_language=source_lang,
                source_text=text,
                translated_text=translation
            )
            
            # If quality is high enough, consider adding as a rule
            if quality_score and quality_score >= 0.8:
                # Check if rule already exists
                existing_rule = TranslationRule.objects.filter(
                    source_language=source_lang,
                    source_text__iexact=text
                ).first()
                
                if not existing_rule:
                    TranslationRule.objects.create(
                        source_language=source_lang,
                        source_text=text,
                        target_text=translation
                    )
        except Exception as e:
            print(f"Error saving to database: {e}")
    
    def get_similar_translations(self, text, source_lang, target_lang, limit=5):
        """Find similar translations from memory for context"""
        # Tokenize input text
        words = set(text.lower().split())
        
        # Search in translation rules
        rules = TranslationRule.objects.filter(source_language=source_lang)
        scored_rules = []
        
        for rule in rules:
            rule_words = set(rule.source_text.lower().split())
            # Calculate similarity score based on word overlap
            overlap = len(words & rule_words)
            total_words = len(words | rule_words)
            similarity = overlap / total_words if total_words > 0 else 0
            
            if similarity > 0.2:  # Minimum similarity threshold
                scored_rules.append((similarity, rule))
        
        # Sort by similarity and return top results
        scored_rules.sort(key=lambda x: x[0], reverse=True)
        return [rule for _, rule in scored_rules[:limit]]
    
    def learn_from_correction(self, original_text, source_lang, target_lang, 
                            original_translation, corrected_translation, correction_type="correction"):
        """Learn from user corrections to improve future translations"""
        try:
            # Save the correction
            correction = TranslationCorrection.objects.create(
                source_language=source_lang,
                target_language=target_lang,
                source_text=original_text,
                original_translation=original_translation,
                corrected_translation=corrected_translation,
                correction_type=correction_type
            )
            
            # Update cache with corrected translation (high quality score)
            self.cache_translation(
                original_text, source_lang, target_lang, 
                corrected_translation, quality_score=0.9, source="user_correction"
            )
            
            # If this is a verified correction, update or create a rule
            if correction_type == "correction":
                rule, created = TranslationRule.objects.update_or_create(
                    source_language=source_lang,
                    source_text=original_text,
                    defaults={'target_text': corrected_translation}
                )
            
            return True
        except Exception as e:
            print(f"Error learning from correction: {e}")
            return False
    
    def get_quality_score(self, text, source_lang, target_lang, translation):
        """Calculate quality score for a translation based on various factors"""
        score = 0.5  # Base score
        
        # Check if translation exists in high-quality sources
        if self._check_translate_py_match(text, translation):
            score += 0.3
        
        if self._check_bible_match(text, translation):
            score += 0.3
        
        # Check user feedback history
        feedback_score = self._get_feedback_score(text, source_lang, target_lang, translation)
        score += feedback_score * 0.2
        
        # Check length appropriateness (not too short or too long)
        length_ratio = len(translation.split()) / max(len(text.split()), 1)
        if 0.5 <= length_ratio <= 2.0:
            score += 0.1
        
        # Ensure score is between 0 and 1
        return min(max(score, 0.0), 1.0)
    
    def _check_translate_py_match(self, text, translation):
        """Check if translation matches translate.py data"""
        # This would check against the loaded translate.py corpus
        # Implementation depends on how translate.py data is structured
        return False  # Placeholder
    
    def _check_bible_match(self, text, translation):
        """Check if translation matches Bible corpus"""
        # This would check against the Bible corpus
        return False  # Placeholder
    
    def _get_feedback_score(self, text, source_lang, target_lang, translation):
        """Get average feedback score for similar translations"""
        try:
            feedback = TranslationFeedback.objects.filter(
                source_language=source_lang,
                target_language=target_lang,
                source_text__icontains=text[:20]  # Partial match
            )
            
            if feedback.exists():
                # Convert feedback types to scores
                score_map = {'good': 1.0, 'partial': 0.5, 'bad': 0.0}
                scores = [score_map.get(f.feedback_type, 0.5) for f in feedback]
                return sum(scores) / len(scores)
            
            return 0.5  # Neutral score if no feedback
        except Exception:
            return 0.5
    
    def get_translation_statistics(self):
        """Get statistics about translation memory performance"""
        try:
            total_translations = TranslationHistory.objects.count()
            total_corrections = TranslationCorrection.objects.count()
            total_feedback = TranslationFeedback.objects.count()
            
            # Calculate correction rate
            correction_rate = (total_corrections / total_translations * 100) if total_translations > 0 else 0
            
            # Get recent activity
            recent_date = datetime.now() - timedelta(days=7)
            recent_translations = TranslationHistory.objects.filter(timestamp__gte=recent_date).count()
            
            return {
                'total_translations': total_translations,
                'total_corrections': total_corrections,
                'total_feedback': total_feedback,
                'correction_rate': round(correction_rate, 2),
                'recent_translations': recent_translations,
                'cache_hit_rate': self._calculate_cache_hit_rate()
            }
        except Exception as e:
            print(f"Error getting statistics: {e}")
            return {}
    
    def _calculate_cache_hit_rate(self):
        """Calculate cache hit rate (placeholder implementation)"""
        # This would require tracking cache hits vs misses
        return 0.0  # Placeholder
    
    def clear_low_quality_cache(self, min_quality=0.3):
        """Clear cached translations below quality threshold"""
        # This would require iterating through cache keys
        # Django's cache doesn't provide easy iteration, so this is a placeholder
        pass
    
    def export_memory(self, format='json'):
        """Export translation memory for backup or analysis"""
        try:
            data = {
                'rules': list(TranslationRule.objects.values()),
                'history': list(TranslationHistory.objects.values()),
                'corrections': list(TranslationCorrection.objects.values()),
                'feedback': list(TranslationFeedback.objects.values()),
                'export_date': datetime.now().isoformat()
            }
            
            if format == 'json':
                return json.dumps(data, default=str, indent=2)
            
            return data
        except Exception as e:
            print(f"Error exporting memory: {e}")
            return None
