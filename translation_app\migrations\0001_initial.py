# Generated by Django 5.2 on 2025-06-03 13:46

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TranslationHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_language', models.<PERSON><PERSON><PERSON><PERSON>(max_length=20)),
                ('source_text', models.TextField()),
                ('translated_text', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='TranslationRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_language', models.Char<PERSON>ield(max_length=20)),
                ('source_text', models.Char<PERSON>ield(max_length=255)),
                ('target_text', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
            ],
        ),
    ]
