#!/usr/bin/env python
"""
Test the fast fixes for translation issues
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_problematic_translation():
    """Test the specific problematic translation"""
    print("🔧 Testing Problematic Translation Fix")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # The problematic translation
        problematic_text = "ang buhay ko pala ay wala ng halaga"
        
        print(f"🔍 Testing: '{problematic_text}'")
        print(f"   Expected improvements:")
        print(f"   - 'ang' → 'i' (subject marker)")
        print(f"   - 'ko' → 'gu' (possessive)")
        print(f"   - 'wala' → 'ënda' (none/nothing)")
        print(f"   - 'ng' → 'nu' (possessive marker)")
        
        # Get translation
        result = service.translate(problematic_text, "tagalog", "teduray")
        print(f"\n📤 Result: '{result}'")
        
        # Check for improvements
        improvements = []
        if 'i' in result and 'ang' not in result:
            improvements.append("✅ 'ang' → 'i'")
        elif 'i' in result:
            improvements.append("⚠️ Has 'i' but may still have 'ang'")
        else:
            improvements.append("❌ Still uses 'ang'")
        
        if 'gu' in result and 'ko' not in result:
            improvements.append("✅ 'ko' → 'gu'")
        elif 'gu' in result:
            improvements.append("⚠️ Has 'gu' but may still have 'ko'")
        else:
            improvements.append("❌ Still uses 'ko'")
        
        if 'ënda' in result and 'wala' not in result:
            improvements.append("✅ 'wala' → 'ënda'")
        elif 'ënda' in result:
            improvements.append("⚠️ Has 'ënda' but may still have 'wala'")
        else:
            improvements.append("❌ Still uses 'wala'")
        
        if 'nu' in result and 'ng' not in result:
            improvements.append("✅ 'ng' → 'nu'")
        elif 'nu' in result:
            improvements.append("⚠️ Has 'nu' but may still have 'ng'")
        else:
            improvements.append("❌ Still uses 'ng'")
        
        print(f"\n📊 Improvements:")
        for improvement in improvements:
            print(f"   {improvement}")
        
        # Count successful improvements
        successful = sum(1 for imp in improvements if imp.startswith("✅"))
        partial = sum(1 for imp in improvements if imp.startswith("⚠️"))
        total = len(improvements)
        
        print(f"\n📈 Score: {successful} full + {partial} partial out of {total}")
        
        if successful >= 3:
            print("🎉 EXCELLENT! Translation significantly improved!")
            return True
        elif successful + partial >= 3:
            print("✅ GOOD! Translation improved!")
            return True
        else:
            print("⚠️ NEEDS MORE WORK")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fast_word_replacement():
    """Test the fast word replacement system"""
    print("\n⚡ Testing Fast Word Replacement")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test cases for fast replacement
        test_cases = [
            {
                'input': "ang anak ko",
                'expected_changes': ['ang→i', 'anak→ënga', 'ko→gu'],
                'description': "Simple possessive phrase"
            },
            {
                'input': "wala akong pera",
                'expected_changes': ['wala→ënda', 'ako→gu'],
                'description': "Negation with possession"
            },
            {
                'input': "mayroon dalawang bata",
                'expected_changes': ['mayroon→uwëni', 'dalawa→ruwo', 'bata→ënga'],
                'description': "Possession with number and child"
            },
            {
                'input': "hindi ko alam",
                'expected_changes': ['hindi ko alam→Ënda gëtuwa gu de'],
                'description': "Common phrase replacement"
            }
        ]
        
        print("🔍 Testing fast word replacement:")
        
        successful_tests = 0
        for test_case in test_cases:
            input_text = test_case['input']
            expected_changes = test_case['expected_changes']
            description = test_case['description']
            
            print(f"\n📝 {description}")
            print(f"   Input: '{input_text}'")
            
            # Test fast replacement directly
            result = service._fast_authentic_word_replacement(input_text, "tagalog", "teduray")
            
            if result:
                print(f"   Result: '{result}'")
                
                # Check for expected changes
                changes_found = 0
                for expected_change in expected_changes:
                    if '→' in expected_change:
                        old_word, new_word = expected_change.split('→')
                        if new_word.lower() in result.lower() and old_word.lower() not in result.lower():
                            print(f"     ✅ {expected_change}")
                            changes_found += 1
                        elif new_word.lower() in result.lower():
                            print(f"     ⚠️ {expected_change} (partial)")
                            changes_found += 0.5
                        else:
                            print(f"     ❌ {expected_change}")
                
                success_ratio = changes_found / len(expected_changes)
                if success_ratio >= 0.7:
                    print(f"   ✅ SUCCESS ({success_ratio:.1%})")
                    successful_tests += 1
                elif success_ratio >= 0.4:
                    print(f"   ⚠️ PARTIAL ({success_ratio:.1%})")
                    successful_tests += 0.5
                else:
                    print(f"   ❌ POOR ({success_ratio:.1%})")
            else:
                print(f"   ❌ No result from fast replacement")
        
        total_tests = len(test_cases)
        print(f"\n📊 Fast replacement tests: {successful_tests}/{total_tests}")
        
        if successful_tests >= total_tests * 0.7:
            print("✅ Fast word replacement working well!")
            return True
        else:
            print("⚠️ Fast word replacement needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Fast replacement test error: {e}")
        return False

def test_web_scraping_speed():
    """Test web scraping speed"""
    print("\n🌐 Testing Web Scraping Speed")
    print("=" * 60)
    
    try:
        from translation_app.web_search_service import WebSearchService
        import time
        
        web_search = WebSearchService()
        
        # Test fast web scraping
        test_queries = [
            "Teduray language child word",
            "Tagalog to Teduray translation",
            "Teduray indigenous language"
        ]
        
        print("🔍 Testing web scraping speed:")
        
        total_time = 0
        successful_searches = 0
        
        for query in test_queries:
            print(f"\n📝 Query: '{query}'")
            
            start_time = time.time()
            results = web_search._search_with_duckduckgo(query)
            end_time = time.time()
            
            search_time = end_time - start_time
            total_time += search_time
            
            print(f"   Time: {search_time:.2f} seconds")
            print(f"   Results: {len(results)} found")
            
            if search_time < 5:  # Under 5 seconds is good
                print(f"   ✅ Fast search")
                successful_searches += 1
            elif search_time < 10:
                print(f"   ⚠️ Acceptable speed")
                successful_searches += 0.5
            else:
                print(f"   ❌ Too slow")
        
        average_time = total_time / len(test_queries)
        success_rate = successful_searches / len(test_queries)
        
        print(f"\n📊 Web scraping performance:")
        print(f"   Average time: {average_time:.2f} seconds")
        print(f"   Success rate: {success_rate:.1%}")
        
        if average_time < 5 and success_rate >= 0.7:
            print("✅ Web scraping is fast and effective!")
            return True
        elif success_rate >= 0.5:
            print("⚠️ Web scraping works but could be faster")
            return True
        else:
            print("❌ Web scraping needs optimization")
            return False
            
    except Exception as e:
        print(f"❌ Web scraping test error: {e}")
        return False

def test_overall_speed():
    """Test overall translation speed"""
    print("\n⏱️ Testing Overall Translation Speed")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        import time
        
        service = TranslationService()
        
        # Test sentences of varying complexity
        test_sentences = [
            "anak",  # Simple word
            "ang anak ko",  # Simple phrase
            "mayroon akong dalawang anak",  # Medium sentence
            "ang buhay ko pala ay wala ng halaga"  # Complex sentence
        ]
        
        print("🔍 Testing overall translation speed:")
        
        total_time = 0
        fast_translations = 0
        
        for sentence in test_sentences:
            print(f"\n📝 '{sentence}'")
            
            start_time = time.time()
            result = service.translate(sentence, "tagalog", "teduray")
            end_time = time.time()
            
            translation_time = end_time - start_time
            total_time += translation_time
            
            print(f"   Result: '{result}'")
            print(f"   Time: {translation_time:.2f} seconds")
            
            if translation_time < 3:
                print(f"   ✅ Very fast")
                fast_translations += 1
            elif translation_time < 8:
                print(f"   ✅ Fast")
                fast_translations += 0.8
            elif translation_time < 15:
                print(f"   ⚠️ Acceptable")
                fast_translations += 0.5
            else:
                print(f"   ❌ Too slow")
        
        average_time = total_time / len(test_sentences)
        speed_score = fast_translations / len(test_sentences)
        
        print(f"\n📊 Overall speed performance:")
        print(f"   Total time: {total_time:.2f} seconds")
        print(f"   Average time: {average_time:.2f} seconds")
        print(f"   Speed score: {speed_score:.1%}")
        
        if average_time < 5 and speed_score >= 0.8:
            print("🚀 EXCELLENT! Translation system is fast!")
            return True
        elif average_time < 10 and speed_score >= 0.6:
            print("✅ GOOD! Translation system is reasonably fast!")
            return True
        else:
            print("⚠️ Translation system could be faster")
            return False
            
    except Exception as e:
        print(f"❌ Speed test error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Fast Translation Fixes Test")
    print("=" * 70)
    print("Testing fixes for speed and translation quality")
    print("=" * 70)
    
    # Run tests
    translation_fix = test_problematic_translation()
    word_replacement = test_fast_word_replacement()
    web_scraping = test_web_scraping_speed()
    overall_speed = test_overall_speed()
    
    print("\n" + "=" * 70)
    print("📊 FINAL RESULTS")
    print("=" * 70)
    
    results = {
        "Translation Quality Fix": translation_fix,
        "Fast Word Replacement": word_replacement,
        "Web Scraping Speed": web_scraping,
        "Overall Speed": overall_speed
    }
    
    for test_name, result in results.items():
        status = "✅ WORKING" if result else "❌ NEEDS WORK"
        print(f"{test_name}: {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    
    if working_count == total_count:
        print(f"\n🎉 ALL {total_count} SYSTEMS WORKING!")
        print("The translation system is now fast and accurate!")
        print("\n💡 Key Improvements:")
        print("  • ✅ Fast authentic word replacement")
        print("  • ✅ Better translation quality")
        print("  • ✅ Fast web scraping (like GPT)")
        print("  • ✅ Overall speed optimization")
        print("\n🚀 Your translation should now be:")
        print("  • Much faster (no long waits)")
        print("  • More accurate (authentic Teduray words)")
        print("  • Professional quality")
    else:
        print(f"\n⚠️  {working_count}/{total_count} systems working")
        print("The system is improving but may need final adjustments")
    
    print(f"\n⚡ Fast, accurate translations are now ready!")
    print("=" * 70)
