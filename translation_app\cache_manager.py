"""
Intelligent Cache Manager for Translation System
Provides advanced caching with TTL, quality-based eviction, and performance optimization.
"""

import json
import hashlib
import time
from datetime import datetime, timedelta
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone


class IntelligentCacheManager:
    """Advanced cache manager with quality-based eviction and intelligent prefetching"""
    
    def __init__(self):
        self.default_ttl = getattr(settings, 'TRANSLATION_CACHE_TTL', 3600 * 24)  # 24 hours
        self.max_cache_size = getattr(settings, 'MAX_TRANSLATION_CACHE_SIZE', 10000)
        self.quality_threshold = 0.7
        self.hit_count_key = 'translation_cache_hits'
        self.miss_count_key = 'translation_cache_misses'
        
        # Cache key prefixes for different types of data
        self.prefixes = {
            'translation': 'trans:',
            'web_search': 'web:',
            'bible': 'bible:',
            'quality': 'quality:',
            'metadata': 'meta:'
        }
    
    def get_cache_key(self, key_type, *args):
        """Generate a standardized cache key"""
        prefix = self.prefixes.get(key_type, 'misc:')
        content = ':'.join(str(arg) for arg in args)
        hash_key = hashlib.md5(content.encode()).hexdigest()
        return f"{prefix}{hash_key}"
    
    def get_translation(self, text, source_lang, target_lang):
        """Get cached translation with hit/miss tracking"""
        cache_key = self.get_cache_key('translation', text, source_lang, target_lang)
        
        cached_data = cache.get(cache_key)
        
        if cached_data:
            self._increment_hit_count()
            
            # Update access time and hit count
            cached_data['last_accessed'] = timezone.now().isoformat()
            cached_data['hit_count'] = cached_data.get('hit_count', 0) + 1
            
            # Extend TTL for frequently accessed items
            if cached_data['hit_count'] > 5:
                ttl = self.default_ttl * 2  # Double TTL for popular items
            else:
                ttl = self.default_ttl
            
            cache.set(cache_key, cached_data, ttl)
            
            return cached_data
        else:
            self._increment_miss_count()
            return None
    
    def set_translation(self, text, source_lang, target_lang, translation_data):
        """Cache translation with intelligent TTL and metadata"""
        cache_key = self.get_cache_key('translation', text, source_lang, target_lang)
        
        # Prepare cache data with metadata
        cache_data = {
            'translation': translation_data.get('translation', ''),
            'quality_score': translation_data.get('quality_score', 0.5),
            'source': translation_data.get('source', 'unknown'),
            'created_at': timezone.now().isoformat(),
            'last_accessed': timezone.now().isoformat(),
            'hit_count': 0,
            'original_text': text,
            'source_lang': source_lang,
            'target_lang': target_lang
        }
        
        # Determine TTL based on quality score
        ttl = self._calculate_ttl(cache_data['quality_score'], cache_data['source'])
        
        # Check cache size and evict if necessary
        self._manage_cache_size()
        
        cache.set(cache_key, cache_data, ttl)
        
        # Update cache metadata
        self._update_cache_metadata(cache_key, cache_data)
    
    def get_web_search_results(self, query, search_type='general'):
        """Get cached web search results"""
        cache_key = self.get_cache_key('web_search', query, search_type)
        
        cached_data = cache.get(cache_key)
        if cached_data:
            self._increment_hit_count()
            return cached_data
        else:
            self._increment_miss_count()
            return None
    
    def set_web_search_results(self, query, search_type, results):
        """Cache web search results with shorter TTL"""
        cache_key = self.get_cache_key('web_search', query, search_type)
        
        cache_data = {
            'results': results,
            'created_at': timezone.now().isoformat(),
            'query': query,
            'search_type': search_type
        }
        
        # Web search results have shorter TTL (1 hour)
        ttl = 3600
        cache.set(cache_key, cache_data, ttl)
    
    def get_bible_verse(self, book, chapter, verse, language):
        """Get cached Bible verse"""
        cache_key = self.get_cache_key('bible', book, chapter, verse, language)
        
        cached_data = cache.get(cache_key)
        if cached_data:
            self._increment_hit_count()
            return cached_data
        else:
            self._increment_miss_count()
            return None
    
    def set_bible_verse(self, book, chapter, verse, language, verse_text):
        """Cache Bible verse with long TTL (Bible text doesn't change)"""
        cache_key = self.get_cache_key('bible', book, chapter, verse, language)
        
        cache_data = {
            'verse_text': verse_text,
            'book': book,
            'chapter': chapter,
            'verse': verse,
            'language': language,
            'created_at': timezone.now().isoformat()
        }
        
        # Bible verses have very long TTL (7 days)
        ttl = 3600 * 24 * 7
        cache.set(cache_key, cache_data, ttl)
    
    def get_quality_score(self, text, source_lang, target_lang, translation):
        """Get cached quality score"""
        cache_key = self.get_cache_key('quality', text, source_lang, target_lang, translation)
        
        cached_data = cache.get(cache_key)
        if cached_data:
            return cached_data.get('quality_score')
        return None
    
    def set_quality_score(self, text, source_lang, target_lang, translation, quality_data):
        """Cache quality score"""
        cache_key = self.get_cache_key('quality', text, source_lang, target_lang, translation)
        
        cache_data = {
            'quality_score': quality_data.get('overall_score', 0.5),
            'component_scores': quality_data.get('component_scores', {}),
            'grade': quality_data.get('grade', 'C'),
            'created_at': timezone.now().isoformat()
        }
        
        # Quality scores have medium TTL (6 hours)
        ttl = 3600 * 6
        cache.set(cache_key, cache_data, ttl)
    
    def _calculate_ttl(self, quality_score, source):
        """Calculate TTL based on quality score and source reliability"""
        base_ttl = self.default_ttl
        
        # Adjust TTL based on quality score
        if quality_score >= 0.9:
            ttl_multiplier = 3.0  # High quality gets longer TTL
        elif quality_score >= 0.7:
            ttl_multiplier = 2.0
        elif quality_score >= 0.5:
            ttl_multiplier = 1.0
        else:
            ttl_multiplier = 0.5  # Low quality gets shorter TTL
        
        # Adjust TTL based on source reliability
        source_multipliers = {
            'translate_py': 2.0,
            'bible_corpus': 2.0,
            'user_correction': 1.8,
            'database_exact': 1.5,
            'gemini_enhanced': 1.2,
            'gemini_basic': 1.0,
            'web_search': 0.8,
            'dictionary': 0.6
        }
        
        source_multiplier = source_multipliers.get(source, 1.0)
        
        final_ttl = int(base_ttl * ttl_multiplier * source_multiplier)
        
        # Ensure TTL is within reasonable bounds
        return max(min(final_ttl, self.default_ttl * 5), 300)  # 5 minutes to 5 days
    
    def _manage_cache_size(self):
        """Manage cache size by evicting low-quality or old entries"""
        # This is a simplified implementation
        # In a real system, you'd need to track cache keys and their metadata
        
        # Get current cache statistics
        stats = self.get_cache_statistics()
        
        # If we're approaching the limit, trigger cleanup
        if stats.get('estimated_size', 0) > self.max_cache_size * 0.8:
            self._evict_low_quality_entries()
    
    def _evict_low_quality_entries(self):
        """Evict low-quality cache entries to make space"""
        # This would require maintaining an index of cache keys
        # For now, this is a placeholder
        pass
    
    def _update_cache_metadata(self, cache_key, cache_data):
        """Update cache metadata for management purposes"""
        metadata_key = self.get_cache_key('metadata', 'cache_index')
        
        # Get existing metadata
        metadata = cache.get(metadata_key, {})
        
        # Update with new entry
        metadata[cache_key] = {
            'quality_score': cache_data.get('quality_score', 0.5),
            'created_at': cache_data.get('created_at'),
            'source': cache_data.get('source', 'unknown'),
            'size_estimate': len(str(cache_data))
        }
        
        # Keep only recent entries in metadata (last 1000)
        if len(metadata) > 1000:
            # Sort by creation time and keep newest 1000
            sorted_items = sorted(
                metadata.items(),
                key=lambda x: x[1].get('created_at', ''),
                reverse=True
            )
            metadata = dict(sorted_items[:1000])
        
        # Cache metadata with long TTL
        cache.set(metadata_key, metadata, self.default_ttl * 2)
    
    def _increment_hit_count(self):
        """Increment cache hit counter"""
        current_hits = cache.get(self.hit_count_key, 0)
        cache.set(self.hit_count_key, current_hits + 1, 3600 * 24)  # 24 hour TTL
    
    def _increment_miss_count(self):
        """Increment cache miss counter"""
        current_misses = cache.get(self.miss_count_key, 0)
        cache.set(self.miss_count_key, current_misses + 1, 3600 * 24)  # 24 hour TTL
    
    def get_cache_statistics(self):
        """Get cache performance statistics"""
        hits = cache.get(self.hit_count_key, 0)
        misses = cache.get(self.miss_count_key, 0)
        total_requests = hits + misses
        
        hit_rate = (hits / total_requests * 100) if total_requests > 0 else 0
        
        # Get metadata for size estimation
        metadata_key = self.get_cache_key('metadata', 'cache_index')
        metadata = cache.get(metadata_key, {})
        
        estimated_size = len(metadata)
        avg_quality = 0
        
        if metadata:
            quality_scores = [entry.get('quality_score', 0.5) for entry in metadata.values()]
            avg_quality = sum(quality_scores) / len(quality_scores)
        
        return {
            'hit_rate': round(hit_rate, 2),
            'total_hits': hits,
            'total_misses': misses,
            'total_requests': total_requests,
            'estimated_size': estimated_size,
            'average_quality': round(avg_quality, 3),
            'cache_efficiency': self._calculate_cache_efficiency(hit_rate, avg_quality)
        }
    
    def _calculate_cache_efficiency(self, hit_rate, avg_quality):
        """Calculate overall cache efficiency score"""
        # Combine hit rate and quality for efficiency score
        efficiency = (hit_rate / 100) * 0.7 + avg_quality * 0.3
        return round(efficiency, 3)
    
    def clear_low_quality_cache(self, min_quality=0.3):
        """Clear cache entries below quality threshold"""
        metadata_key = self.get_cache_key('metadata', 'cache_index')
        metadata = cache.get(metadata_key, {})
        
        keys_to_delete = []
        
        for cache_key, entry_metadata in metadata.items():
            if entry_metadata.get('quality_score', 0.5) < min_quality:
                keys_to_delete.append(cache_key)
        
        # Delete low-quality entries
        for key in keys_to_delete:
            cache.delete(key)
            del metadata[key]
        
        # Update metadata
        cache.set(metadata_key, metadata, self.default_ttl * 2)
        
        return len(keys_to_delete)
    
    def warm_cache(self, common_phrases):
        """Pre-warm cache with common phrases"""
        # This would pre-translate common phrases and cache them
        # Implementation would depend on having a list of common phrases
        pass
    
    def export_cache_data(self):
        """Export cache data for analysis or backup"""
        metadata_key = self.get_cache_key('metadata', 'cache_index')
        metadata = cache.get(metadata_key, {})
        
        export_data = {
            'statistics': self.get_cache_statistics(),
            'metadata': metadata,
            'export_timestamp': timezone.now().isoformat()
        }
        
        return export_data
