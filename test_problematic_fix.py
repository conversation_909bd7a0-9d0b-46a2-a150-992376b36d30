#!/usr/bin/env python
"""
Test the fix for the problematic translation
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_problematic_translation_fix():
    """Test the specific problematic translation"""
    print("🔧 Testing Problematic Translation Fix")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # The problematic translation
        problematic_text = "ang saya pala kapag kasama ka"
        
        print(f"🔍 Testing: '{problematic_text}'")
        print(f"   Current result: 'dëb saya pala kapag kasama ëm' (Grade D - 53%)")
        print(f"   Expected improvements:")
        print(f"   - 'saya' → 'mëfiya' (happy)")
        print(f"   - 'kapag' → 'kun' (when)")
        print(f"   - 'kasama' → 'kësama' (together)")
        
        # Get translation
        result = service.translate(problematic_text, "tagalog", "teduray")
        print(f"\n📤 New Result: '{result}'")
        
        # Check for improvements
        improvements = []
        
        # Check if new words are translated
        if 'mëfiya' in result and 'saya' not in result:
            improvements.append("✅ 'saya' → 'mëfiya' (happy)")
        elif 'mëfiya' in result:
            improvements.append("⚠️ Has 'mëfiya' but may still have 'saya'")
        else:
            improvements.append("❌ Still uses 'saya' instead of 'mëfiya'")
        
        if 'kun' in result and 'kapag' not in result:
            improvements.append("✅ 'kapag' → 'kun' (when)")
        elif 'kun' in result:
            improvements.append("⚠️ Has 'kun' but may still have 'kapag'")
        else:
            improvements.append("❌ Still uses 'kapag' instead of 'kun'")
        
        if 'kësama' in result and 'kasama' not in result:
            improvements.append("✅ 'kasama' → 'kësama' (together)")
        elif 'kësama' in result:
            improvements.append("⚠️ Has 'kësama' but may still have 'kasama'")
        else:
            improvements.append("❌ Still uses 'kasama' instead of 'kësama'")
        
        # Check existing improvements
        if 'dëb' in result and 'ang' not in result:
            improvements.append("✅ 'ang' → 'dëb' (already working)")
        
        if 'ëm' in result and 'ka' not in result:
            improvements.append("✅ 'ka' → 'ëm' (already working)")
        
        print(f"\n📊 Improvements:")
        for improvement in improvements:
            print(f"   {improvement}")
        
        # Count successful improvements
        successful = sum(1 for imp in improvements if imp.startswith("✅"))
        partial = sum(1 for imp in improvements if imp.startswith("⚠️"))
        total = len(improvements)
        
        improvement_score = (successful + partial * 0.5) / total
        
        print(f"\n📈 Improvement Score: {improvement_score:.1%} ({successful} full + {partial} partial out of {total})")
        
        # Calculate estimated quality
        words_translated = successful + partial * 0.5
        total_words = 6  # ang saya pala kapag kasama ka
        estimated_quality = (words_translated / total_words) * 100
        
        print(f"📊 Estimated Quality: {estimated_quality:.1f}%")
        
        if estimated_quality >= 80:
            grade = "A"
        elif estimated_quality >= 70:
            grade = "B"
        elif estimated_quality >= 60:
            grade = "C"
        else:
            grade = "D"
        
        print(f"📊 Estimated Grade: {grade}")
        
        if improvement_score >= 0.8:
            print("🎉 EXCELLENT! Translation greatly improved!")
            return True
        elif improvement_score >= 0.6:
            print("✅ GOOD! Translation improved!")
            return True
        else:
            print("⚠️ Translation still needs work")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fast_replacement_directly():
    """Test the fast replacement method directly"""
    print("\n⚡ Testing Fast Replacement Directly")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test the fast replacement directly
        test_text = "ang saya pala kapag kasama ka"
        
        print(f"🔍 Testing fast replacement with: '{test_text}'")
        
        result = service._fast_authentic_word_replacement(test_text, "tagalog", "teduray")
        
        if result:
            print(f"📤 Fast replacement result: '{result}'")
            
            # Count how many words were translated
            original_words = test_text.split()
            result_words = result.split()
            
            translated_count = 0
            for i, (orig, trans) in enumerate(zip(original_words, result_words)):
                if orig.lower() != trans.lower():
                    translated_count += 1
                    print(f"   ✅ Word {i+1}: '{orig}' → '{trans}'")
                else:
                    print(f"   ❌ Word {i+1}: '{orig}' (unchanged)")
            
            translation_rate = translated_count / len(original_words)
            print(f"\n📊 Translation rate: {translation_rate:.1%} ({translated_count}/{len(original_words)} words)")
            
            if translation_rate >= 0.8:
                print("🎉 EXCELLENT! Fast replacement working great!")
                return True
            elif translation_rate >= 0.6:
                print("✅ GOOD! Fast replacement working well!")
                return True
            else:
                print("⚠️ Fast replacement needs more vocabulary")
                return False
        else:
            print("❌ Fast replacement returned no result")
            return False
            
    except Exception as e:
        print(f"❌ Fast replacement test error: {e}")
        return False

def test_vocabulary_coverage():
    """Test vocabulary coverage for common words"""
    print("\n📚 Testing Vocabulary Coverage")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test common words that should be in vocabulary
        test_words = [
            "saya",      # happy
            "kapag",     # when
            "kasama",    # together
            "masaya",    # happy
            "maganda",   # beautiful
            "mabuti",    # good
            "gusto",     # want
            "ayaw",      # don't want
            "tubig",     # water
            "pagkain",   # food
            "bahay",     # house
            "trabaho",   # work
            "araw",      # day
            "gabi",      # night
        ]
        
        print("🔍 Testing vocabulary coverage:")
        
        covered_words = 0
        for word in test_words:
            print(f"\n📝 Testing: '{word}'")
            
            result = service._fast_authentic_word_replacement(word, "tagalog", "teduray")
            
            if result and result.lower() != word.lower():
                print(f"   ✅ Covered: '{word}' → '{result}'")
                covered_words += 1
            else:
                print(f"   ❌ Not covered: '{word}' (unchanged)")
        
        coverage_rate = covered_words / len(test_words)
        print(f"\n📊 Vocabulary coverage: {coverage_rate:.1%} ({covered_words}/{len(test_words)} words)")
        
        if coverage_rate >= 0.8:
            print("🎉 EXCELLENT! Vocabulary coverage is great!")
            return True
        elif coverage_rate >= 0.6:
            print("✅ GOOD! Vocabulary coverage is decent!")
            return True
        else:
            print("⚠️ Vocabulary coverage needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Vocabulary coverage test error: {e}")
        return False

def test_multiple_problematic_sentences():
    """Test multiple sentences that might have similar issues"""
    print("\n📝 Testing Multiple Problematic Sentences")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test sentences with similar patterns
        test_sentences = [
            "ang saya pala kapag kasama ka",
            "masaya ako kapag kasama mo",
            "gusto ko ang pagkain mo",
            "ayaw niya ng trabaho",
            "maganda ang bahay nila"
        ]
        
        print("🔍 Testing multiple problematic sentences:")
        
        successful_translations = 0
        for sentence in test_sentences:
            print(f"\n📝 Testing: '{sentence}'")
            
            result = service.translate(sentence, "tagalog", "teduray")
            print(f"   Result: '{result}'")
            
            # Count translated words
            original_words = sentence.split()
            result_words = result.split()
            
            translated_count = 0
            for orig, trans in zip(original_words, result_words):
                if orig.lower() != trans.lower():
                    translated_count += 1
            
            translation_rate = translated_count / len(original_words)
            print(f"   Translation rate: {translation_rate:.1%}")
            
            if translation_rate >= 0.7:
                print(f"   ✅ GOOD translation")
                successful_translations += 1
            elif translation_rate >= 0.5:
                print(f"   ⚠️ PARTIAL translation")
                successful_translations += 0.5
            else:
                print(f"   ❌ POOR translation")
        
        success_rate = successful_translations / len(test_sentences)
        print(f"\n📊 Overall success rate: {success_rate:.1%}")
        
        if success_rate >= 0.8:
            print("🎉 EXCELLENT! Multiple sentences working great!")
            return True
        elif success_rate >= 0.6:
            print("✅ GOOD! Multiple sentences mostly working!")
            return True
        else:
            print("⚠️ Multiple sentences need improvement")
            return False
            
    except Exception as e:
        print(f"❌ Multiple sentences test error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Problematic Translation Fix Test")
    print("=" * 70)
    print("Testing fixes for the Grade D translation issue")
    print("=" * 70)
    
    # Run tests
    translation_fix = test_problematic_translation_fix()
    fast_replacement = test_fast_replacement_directly()
    vocabulary_coverage = test_vocabulary_coverage()
    multiple_sentences = test_multiple_problematic_sentences()
    
    print("\n" + "=" * 70)
    print("📊 PROBLEMATIC TRANSLATION FIX RESULTS")
    print("=" * 70)
    
    results = {
        "Problematic Translation Fix": translation_fix,
        "Fast Replacement": fast_replacement,
        "Vocabulary Coverage": vocabulary_coverage,
        "Multiple Sentences": multiple_sentences
    }
    
    for test_name, result in results.items():
        status = "✅ WORKING" if result else "❌ NEEDS WORK"
        print(f"{test_name}: {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    fix_score = (working_count / total_count) * 100
    
    print(f"\n📈 Fix Score: {fix_score:.1f}%")
    
    if working_count == total_count:
        print(f"\n🎉 PERFECT FIX!")
        print("The problematic translation is now working perfectly!")
        print("\n💡 Fixed Issues:")
        print("  • ✅ Added missing vocabulary")
        print("  • ✅ Improved fast replacement")
        print("  • ✅ Better word coverage")
        print("  • ✅ Multiple sentence support")
        print("\n🚀 Expected improvements:")
        print("  • Grade D → Grade A/B")
        print("  • 53% → 80%+ quality")
        print("  • All words properly translated")
        print("  • Professional Teduray output")
    elif fix_score >= 75:
        print(f"\n🎯 EXCELLENT! {fix_score:.1f}% fix achieved!")
        print("The problematic translation is much better")
    elif fix_score >= 50:
        print(f"\n✅ GOOD! {fix_score:.1f}% fix achieved!")
        print("The problematic translation is improved")
    else:
        print(f"\n⚠️ {fix_score:.1f}% fix - needs more work")
        print("The problematic translation still needs improvement")
    
    print(f"\n🔧 Translation Fix: {'COMPLETE' if fix_score >= 75 else 'IN PROGRESS'}")
    print("=" * 70)
