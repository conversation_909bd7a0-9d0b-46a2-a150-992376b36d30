#!/usr/bin/env python
"""
Add basic words to the translation database
These are essential words that should be in the database for proper translation.
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def add_basic_words():
    """Add essential basic words to the translation database"""
    print("📚 Adding Basic Words to Translation Database")
    print("=" * 50)
    
    try:
        from translation_app.models import TranslationRule
        
        # Essential basic words that should be in the database
        basic_words = [
            # Verbs
            ('kumain', 'menama', 'verb', 'to eat'),
            ('uminom', 'minum', 'verb', 'to drink'),
            ('natulog', 'meturug', 'verb', 'slept'),
            ('gumising', 'mebangon', 'verb', 'woke up'),
            ('tumakbo', 'melayag', 'verb', 'ran'),
            ('lumakad', 'melakad', 'verb', 'walked'),
            ('magluto', 'meluto', 'verb', 'to cook'),
            ('magbasa', 'mebasa', 'verb', 'to read'),
            ('magsulat', 'mesulat', 'verb', 'to write'),
            ('matulog', 'meturug', 'verb', 'to sleep'),
            
            # Nouns - People
            ('lalaki', 'lagey', 'noun', 'man'),
            ('babae', 'libun', 'noun', 'woman'),
            ('bata', 'anak', 'noun', 'child'),
            ('matanda', 'lukes', 'noun', 'old person'),
            ('tao', 'tau', 'noun', 'person'),
            ('ama', 'bapa', 'noun', 'father'),
            ('ina', 'ina', 'noun', 'mother'),
            ('anak', 'anak', 'noun', 'child'),
            ('kuya', 'kaka', 'noun', 'older brother'),
            ('ate', 'kaka', 'noun', 'older sister'),
            
            # Nouns - Food
            ('tinapay', 'fan', 'noun', 'bread'),
            ('kanin', 'nasi', 'noun', 'rice'),
            ('tubig', 'weg', 'noun', 'water'),
            ('pagkain', 'kanen', 'noun', 'food'),
            ('karne', 'karne', 'noun', 'meat'),
            ('isda', 'sada', 'noun', 'fish'),
            ('gulay', 'gulay', 'noun', 'vegetable'),
            ('prutas', 'buah', 'noun', 'fruit'),
            
            # Adjectives
            ('matandang', 'lukës', 'adjective', 'old'),
            ('batang', 'anak', 'adjective', 'young'),
            ('malaking', 'maragang', 'adjective', 'big'),
            ('maliit', 'ketek', 'adjective', 'small'),
            ('maganda', 'mefiya', 'adjective', 'beautiful'),
            ('pangit', 'meaat', 'adjective', 'ugly'),
            ('mabait', 'mefiya', 'adjective', 'kind'),
            ('masama', 'meaat', 'adjective', 'bad'),
            
            # Pronouns
            ('ako', 'aku', 'pronoun', 'I'),
            ('ikaw', 'ikew', 'pronoun', 'you'),
            ('siya', 'siran', 'pronoun', 'he/she'),
            ('kami', 'kami', 'pronoun', 'we (exclusive)'),
            ('tayo', 'kita', 'pronoun', 'we (inclusive)'),
            ('kayo', 'kamu', 'pronoun', 'you (plural)'),
            ('sila', 'siran', 'pronoun', 'they'),
            ('ka', 'kew', 'pronoun', 'you'),
            
            # Question words
            ('ano', 'unu', 'question', 'what'),
            ('sino', 'sinew', 'question', 'who'),
            ('saan', 'diin', 'question', 'where'),
            ('kailan', 'kanu', 'question', 'when'),
            ('bakit', 'nguda', 'question', 'why'),
            ('paano', 'piye', 'question', 'how'),
            ('kumusta', 'kumusta', 'question', 'how are you'),
            
            # Common phrases
            ('salamat', 'salamat', 'phrase', 'thank you'),
            ('paalam', 'paalam', 'phrase', 'goodbye'),
            ('oo', 'iyo', 'phrase', 'yes'),
            ('hindi', 'dii', 'phrase', 'no'),
            ('mahal kita', 'malago taku', 'phrase', 'I love you'),
            
            # Prepositions and particles
            ('sa', 'de', 'preposition', 'in/at/to'),
            ('ng', 'nu', 'particle', 'of/by'),
            ('kay', 'kay', 'preposition', 'to/for (person)'),
            ('para', 'para', 'preposition', 'for'),
            ('dahil', 'sebab', 'preposition', 'because'),
            ('at', 'wey', 'conjunction', 'and'),
            ('o', 'o', 'conjunction', 'or'),
            ('ang', 'i', 'particle', 'the (subject marker)'),
            
            # Time words
            ('umaga', 'umaga', 'noun', 'morning'),
            ('tanghali', 'tengahari', 'noun', 'noon'),
            ('hapon', 'petang', 'noun', 'afternoon'),
            ('gabi', 'melem', 'noun', 'night'),
            ('ngayon', 'initu', 'adverb', 'now'),
            ('bukas', 'bukas', 'adverb', 'tomorrow'),
            ('kahapon', 'kemarin', 'adverb', 'yesterday'),
            
            # Places
            ('bahay', 'balay', 'noun', 'house'),
            ('eskwela', 'sekolah', 'noun', 'school'),
            ('simbahan', 'mesjid', 'noun', 'church'),
            ('palengke', 'pasar', 'noun', 'market'),
            ('ospital', 'rumah sakit', 'noun', 'hospital'),
            
            # Body parts
            ('mata', 'mata', 'noun', 'eye'),
            ('ilong', 'idung', 'noun', 'nose'),
            ('bibig', 'baba', 'noun', 'mouth'),
            ('kamay', 'tangan', 'noun', 'hand'),
            ('paa', 'kaki', 'noun', 'foot'),
            ('ulo', 'kepala', 'noun', 'head'),
            
            # Colors
            ('puti', 'putih', 'adjective', 'white'),
            ('itim', 'hitam', 'adjective', 'black'),
            ('pula', 'merah', 'adjective', 'red'),
            ('asul', 'biru', 'adjective', 'blue'),
            ('dilaw', 'kuning', 'adjective', 'yellow'),
            ('berde', 'hijau', 'adjective', 'green'),
        ]
        
        added_count = 0
        updated_count = 0
        
        for tagalog, teduray, word_type, meaning in basic_words:
            try:
                # Check if the word already exists
                existing_rule = TranslationRule.objects.filter(
                    source_language='tagalog',
                    source_text__iexact=tagalog
                ).first()
                
                if existing_rule:
                    # Update if different
                    if existing_rule.target_text != teduray:
                        existing_rule.target_text = teduray
                        existing_rule.save()
                        updated_count += 1
                        print(f"   Updated: '{tagalog}' → '{teduray}' (was: '{existing_rule.target_text}')")
                else:
                    # Add new rule
                    TranslationRule.objects.create(
                        source_language='tagalog',
                        source_text=tagalog,
                        target_text=teduray
                    )
                    added_count += 1
                    print(f"   Added: '{tagalog}' → '{teduray}' ({word_type})")
                
                # Also add reverse translation (Teduray to Tagalog)
                reverse_rule = TranslationRule.objects.filter(
                    source_language='teduray',
                    source_text__iexact=teduray
                ).first()
                
                if not reverse_rule:
                    TranslationRule.objects.create(
                        source_language='teduray',
                        source_text=teduray,
                        target_text=tagalog
                    )
                
            except Exception as e:
                print(f"   Error adding '{tagalog}': {e}")
        
        print(f"\n✅ Basic words processing completed!")
        print(f"   Added: {added_count} new words")
        print(f"   Updated: {updated_count} existing words")
        print(f"   Total processed: {len(basic_words)} words")
        
        # Verify some key words
        print(f"\n🔍 Verifying key words:")
        test_words = ['kumain', 'lalaki', 'matanda', 'tinapay']
        for word in test_words:
            rule = TranslationRule.objects.filter(
                source_language='tagalog',
                source_text__iexact=word
            ).first()
            
            if rule:
                print(f"   ✅ '{word}' → '{rule.target_text}'")
            else:
                print(f"   ❌ '{word}' → Not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error adding basic words: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_common_phrases():
    """Add common phrases and sentence patterns"""
    print("\n📝 Adding Common Phrases and Patterns")
    print("=" * 50)
    
    try:
        from translation_app.models import TranslationRule
        
        # Common phrases and sentence patterns
        phrases = [
            # Greetings
            ('Kumusta ka?', 'Kumusta kew?', 'greeting'),
            ('Magandang umaga', 'Mefiya umaga', 'greeting'),
            ('Magandang hapon', 'Mefiya petang', 'greeting'),
            ('Magandang gabi', 'Mefiya melem', 'greeting'),
            
            # Common expressions
            ('Salamat po', 'Salamat', 'courtesy'),
            ('Walang anuman', 'Dii unu-unu', 'courtesy'),
            ('Pasensya na', 'Maaf', 'courtesy'),
            ('Paalam na', 'Paalam na', 'farewell'),
            
            # Questions
            ('Ano ang pangalan mo?', 'Unu ngaran mu?', 'question'),
            ('Saan ka nakatira?', 'Diin kew meninggal?', 'question'),
            ('Ilang taon ka na?', 'Pira taun mu na?', 'question'),
            ('Kumain ka na ba?', 'Menama kew na ba?', 'question'),
            
            # Common sentences
            ('Mahal ko ang aking pamilya', 'Malago ku i keluarga ku', 'statement'),
            ('Salamat sa Panginoon', 'Salamat de Tuhan', 'religious'),
            ('Kumain ang matandang lalaki', 'Menama i lukës lagey', 'sentence'),
            ('Uminom ng tubig ang bata', 'Minum weg i anak', 'sentence'),
            
            # Compound phrases
            ('matandang lalaki', 'lukës lagey', 'noun_phrase'),
            ('batang babae', 'anak libun', 'noun_phrase'),
            ('malaking bahay', 'maragang balay', 'noun_phrase'),
            ('maliit na bata', 'ketek anak', 'noun_phrase'),
            
            # Verb phrases
            ('kumain ng tinapay', 'menama fan', 'verb_phrase'),
            ('uminom ng tubig', 'minum weg', 'verb_phrase'),
            ('magluto ng pagkain', 'meluto kanen', 'verb_phrase'),
            ('magbasa ng libro', 'mebasa buku', 'verb_phrase'),
        ]
        
        added_count = 0
        
        for tagalog, teduray, phrase_type in phrases:
            try:
                # Check if phrase already exists
                existing_rule = TranslationRule.objects.filter(
                    source_language='tagalog',
                    source_text__iexact=tagalog
                ).first()
                
                if not existing_rule:
                    TranslationRule.objects.create(
                        source_language='tagalog',
                        source_text=tagalog,
                        target_text=teduray
                    )
                    added_count += 1
                    print(f"   Added phrase: '{tagalog}' → '{teduray}'")
                
                # Add reverse
                reverse_rule = TranslationRule.objects.filter(
                    source_language='teduray',
                    source_text__iexact=teduray
                ).first()
                
                if not reverse_rule:
                    TranslationRule.objects.create(
                        source_language='teduray',
                        source_text=teduray,
                        target_text=tagalog
                    )
                
            except Exception as e:
                print(f"   Error adding phrase '{tagalog}': {e}")
        
        print(f"\n✅ Phrases processing completed!")
        print(f"   Added: {added_count} new phrases")
        
        return True
        
    except Exception as e:
        print(f"❌ Error adding phrases: {e}")
        return False

if __name__ == "__main__":
    print("📚 Adding Essential Translation Data")
    print("=" * 60)
    
    # Add basic words
    words_success = add_basic_words()
    
    # Add common phrases
    phrases_success = add_common_phrases()
    
    print("\n" + "=" * 60)
    print("📊 RESULTS SUMMARY")
    print("=" * 60)
    
    if words_success:
        print("✅ Basic Words: ADDED")
    else:
        print("❌ Basic Words: FAILED")
    
    if phrases_success:
        print("✅ Common Phrases: ADDED")
    else:
        print("❌ Common Phrases: FAILED")
    
    if words_success and phrases_success:
        print("\n🎉 ALL ESSENTIAL DATA ADDED!")
        print("Your translation system now has the basic vocabulary needed")
        print("for proper hierarchical translation!")
        print("\nTest the system again with:")
        print("python debug_translation.py")
    else:
        print("\n⚠️  Some data addition failed. Check the errors above.")
    
    print("\n" + "=" * 60)
