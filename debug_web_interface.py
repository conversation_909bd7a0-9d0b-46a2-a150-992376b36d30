#!/usr/bin/env python
"""
Debug the exact same flow as the web interface to find the issue
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def debug_web_interface_flow():
    """Debug the exact same flow as the web interface"""
    print("🐛 Debugging Web Interface Flow")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationHistory
        
        # Simulate the exact web interface request
        text = "masaya ang buhay kung may nag mamahal sayo"
        source_lang = "tagalog"
        target_lang = "teduray"
        
        print(f"🔍 Testing: '{text}'")
        print(f"   Source: {source_lang}")
        print(f"   Target: {target_lang}")
        
        # Create service exactly like the web interface (line 28)
        service = TranslationService()
        
        # Call translate exactly like the web interface (line 31)
        translated_text = service.translate(text, source_lang, target_lang)
        
        print(f"\n📤 Web Interface Result: '{translated_text}'")
        
        # Get quality score exactly like the web interface (lines 34-36)
        quality_data = service.quality_scorer.score_translation(
            text, translated_text, source_lang, target_lang, 'api_request'
        )
        
        print(f"📊 Quality Score: {quality_data.get('overall_score', 0.5):.1%}")
        print(f"📊 Quality Grade: {quality_data.get('grade', 'C')}")
        
        # Get translation statistics exactly like the web interface (line 46)
        stats = service.get_translation_statistics()
        
        print(f"📊 Cache Hit Rate: {stats['performance_summary']['cache_hit_rate']:.1%}")
        
        # Now debug step by step to see what's happening
        print(f"\n🔬 Step-by-Step Debug:")
        
        # Check if it's using hierarchical translation
        print(f"   Text length: {len(text.split())} words")
        is_simple = service._is_simple_text(text)
        print(f"   Is simple text: {is_simple}")
        
        if not is_simple:
            print(f"   ✅ Should use hierarchical translation")
            
            # Test hierarchical translation directly
            hierarchical_result = service._hierarchical_translate(text, source_lang, target_lang)
            if hierarchical_result:
                print(f"   ✅ Hierarchical result: '{hierarchical_result}'")
                
                if translated_text == hierarchical_result:
                    print(f"   ✅ Web interface used hierarchical result!")
                else:
                    print(f"   ❌ Web interface used different result")
                    print(f"       Expected: '{hierarchical_result}'")
                    print(f"       Got: '{translated_text}'")
            else:
                print(f"   ❌ Hierarchical translation failed")
        else:
            print(f"   ℹ️  Simple text, should use database")
        
        # Check each step of the pipeline manually
        print(f"\n🔍 Pipeline Debug:")
        
        # Step 1: Cache
        cached = service.cache_manager.get_translation(text, source_lang, target_lang)
        if cached:
            print(f"   Step 1 - Cache: '{cached['translation']}' (source: {cached.get('source', 'unknown')})")
        else:
            print("   Step 1 - Cache: No hit")
        
        # Step 2: Bible
        bible_result = service._check_bible_corpus(text, source_lang, target_lang)
        if bible_result:
            print(f"   Step 2 - Bible: '{bible_result}'")
        else:
            print("   Step 2 - Bible: No match")
        
        # Step 3: translate.py
        py_result = service._check_translate_py(text, source_lang, target_lang)
        if py_result:
            print(f"   Step 3 - translate.py: '{py_result}'")
        else:
            print("   Step 3 - translate.py: No match")
        
        # Step 4: Database
        db_result = service._check_database_matches(text, source_lang, target_lang)
        is_high_quality = service._is_high_quality_match(text, db_result) if db_result else False
        
        print(f"   Step 4 - Database: '{db_result}' (simple: {is_simple}, high_quality: {is_high_quality})")
        
        if db_result and is_high_quality and is_simple:
            print(f"   ⚠️  Database result would be used (simple text)")
        elif db_result and is_high_quality and not is_simple:
            print(f"   ⚠️  Database result would be skipped (complex text)")
        
        # Step 5: Hierarchical
        if not is_simple:
            hierarchical_result = service._hierarchical_translate(text, source_lang, target_lang)
            if hierarchical_result:
                print(f"   Step 5 - Hierarchical: '{hierarchical_result}'")
            else:
                print("   Step 5 - Hierarchical: Failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hierarchical_directly():
    """Test hierarchical translator directly with the problematic sentence"""
    print("\n🧠 Testing Hierarchical Translator Directly")
    print("=" * 50)
    
    try:
        from translation_app.hierarchical_translator import HierarchicalTranslator
        from translation_app.models import TranslationRule
        
        translator = HierarchicalTranslator()
        
        text = "masaya ang buhay kung may nag mamahal sayo"
        source_lang = "tagalog"
        target_lang = "teduray"
        
        print(f"🔍 Testing: '{text}'")
        
        # Get context rules like the service does
        context_rules = TranslationRule.objects.filter(source_language=source_lang)[:30]
        print(f"   Context rules: {len(context_rules)} rules")
        
        # Test hierarchical translation
        result = translator.translate_hierarchically(text, source_lang, target_lang, context_rules)
        
        if result:
            print(f"✅ Hierarchical translation successful!")
            print(f"   Input: '{text}'")
            print(f"   Output: '{result['translation']}'")
            print(f"   Level: {result['level']}")
            print(f"   Method: {result['method']}")
            
            if 'phrases' in result:
                print(f"   Phrases detected:")
                for phrase in result['phrases']:
                    print(f"     - '{phrase['original']}' → '{phrase['translation']}' ({phrase['type']})")
        else:
            print(f"❌ Hierarchical translation failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Hierarchical test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_service_initialization():
    """Check if the service is initializing correctly"""
    print("\n⚙️ Checking Service Initialization")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        print("🔍 Creating TranslationService...")
        service = TranslationService()
        
        print(f"   ✅ Service created")
        print(f"   ✅ Hierarchical translator: {hasattr(service, 'hierarchical_translator')}")
        print(f"   ✅ Quality scorer: {hasattr(service, 'quality_scorer')}")
        print(f"   ✅ Cache manager: {hasattr(service, 'cache_manager')}")
        print(f"   ✅ Translation memory: {hasattr(service, 'translation_memory')}")
        print(f"   ✅ Web search: {hasattr(service, 'web_search')}")
        
        # Check translate.py data
        if hasattr(service, 'translate_py_data'):
            print(f"   ✅ translate.py data: {len(service.translate_py_data)} entries")
        else:
            print(f"   ❌ translate.py data: Not loaded")
        
        # Check if methods exist
        methods_to_check = [
            '_hierarchical_translate',
            '_is_simple_text',
            '_check_database_matches',
            '_handle_unknown_words'
        ]
        
        for method in methods_to_check:
            if hasattr(service, method):
                print(f"   ✅ Method {method}: Available")
            else:
                print(f"   ❌ Method {method}: Missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Service initialization error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Web Interface Debug")
    print("=" * 60)
    
    # Run debug tests
    service_test = check_service_initialization()
    web_test = debug_web_interface_flow()
    hierarchical_test = test_hierarchical_directly()
    
    print("\n" + "=" * 60)
    print("📊 DEBUG RESULTS")
    print("=" * 60)
    
    if service_test:
        print("✅ Service Initialization: PASSED")
    else:
        print("❌ Service Initialization: FAILED")
    
    if web_test:
        print("✅ Web Interface Flow: PASSED")
    else:
        print("❌ Web Interface Flow: FAILED")
    
    if hierarchical_test:
        print("✅ Hierarchical Direct Test: PASSED")
    else:
        print("❌ Hierarchical Direct Test: FAILED")
    
    if service_test and web_test and hierarchical_test:
        print("\n🎉 ALL DEBUG TESTS PASSED!")
        print("The issue should be identified in the output above.")
    else:
        print("\n⚠️  Some debug tests failed. Check the output above for the root cause.")
    
    print("\n" + "=" * 60)
