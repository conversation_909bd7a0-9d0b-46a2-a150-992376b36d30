# Professional Translation System Setup Guide

## Overview

Your translation system has been upgraded with professional-grade features including:

- **Intelligent Caching System** - High-performance caching with quality-based eviction
- **Advanced Web Search Integration** - Multi-source web search with SerpAPI and Google fallback
- **Translation Memory System** - Learning from corrections and maintaining translation quality
- **Quality Scoring Engine** - Comprehensive quality assessment for all translations
- **Professional AI Integration** - Enhanced Gemini prompts with context and web search
- **Performance Monitoring** - Real-time statistics and performance tracking

## Installation Steps

### 1. Install Dependencies

```bash
# Create and activate virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install required packages
pip install -r requirements.txt
```

### 2. Database Setup

```bash
# Run migrations to create new tables
python manage.py makemigrations
python manage.py migrate
```

### 3. Configuration

The system is pre-configured with:
- **Gemini API Key**: Already set in settings.py
- **SerpAPI Key**: Already configured for web search
- **Cache Settings**: Optimized for performance

### 4. Optional: Install Additional Dependencies

For better performance, install these optional packages:

```bash
# For Redis caching (production recommended)
pip install redis django-redis

# For better image processing
pip install pillow-simd

# For advanced NLP features
pip install spacy
python -m spacy download en_core_web_sm
```

## New Features Overview

### 1. Professional Translation Pipeline

The new translation system follows this intelligent pipeline:

1. **Cache Check** - Instant results for previously translated text
2. **Bible Corpus** - Exact matches from Teduray Bible verses
3. **Translate.py** - Authoritative translations from your curated data
4. **Database Matches** - Flexible matching with punctuation handling
5. **AI Translation** - Enhanced Gemini with web search context
6. **Quality Scoring** - Automatic quality assessment and caching

### 2. Web Search Integration

- **Multiple Search Engines**: SerpAPI (premium) with Google fallback
- **Intelligent Queries**: Context-aware search query generation
- **Result Processing**: Automatic translation extraction from search results
- **Caching**: Search results are cached to improve performance

### 3. Translation Memory

- **Learning System**: Automatically learns from user corrections
- **Quality Tracking**: Maintains quality scores for all translations
- **Similar Translations**: Finds related translations for context
- **Export/Import**: Backup and restore translation data

### 4. Quality Scoring

Translations are scored based on:
- **Source Authority** (95% for translate.py, 90% for Bible, etc.)
- **Linguistic Quality** (grammar, structure, markers)
- **Consistency** (with existing translations)
- **User Feedback** (historical feedback scores)
- **Length Appropriateness** (reasonable length ratios)
- **Completeness** (all words translated)
- **Cultural Appropriateness** (Teduray cultural context)

### 5. Performance Monitoring

Real-time tracking of:
- Cache hit rates
- Translation quality averages
- Gemini API usage
- Web search frequency
- User correction rates

## API Endpoints

### New Professional Endpoints

1. **Translation Statistics**
   ```
   GET /api/statistics/
   ```

2. **Clear Low-Quality Cache**
   ```
   POST /api/clear-cache/
   Data: min_quality (float, default: 0.3)
   ```

3. **Export Translation Data**
   ```
   GET /api/export-data/
   ```

4. **Web Search Translation**
   ```
   POST /api/web-search/
   Data: text, source_lang, search_type
   ```

### Enhanced Translation API

The main translation endpoint now returns:
```json
{
    "translated_text": "...",
    "quality_score": 0.85,
    "quality_grade": "A",
    "recommendations": ["..."],
    "performance_stats": {
        "cache_hit_rate": 75.5,
        "total_translations": 1250
    }
}
```

## User Interface Enhancements

### New Features in Web Interface

1. **Quality Indicators**: Real-time quality scores and grades
2. **Performance Stats**: Cache hit rates and system performance
3. **Web Search Button**: Direct access to web search functionality
4. **Statistics Dashboard**: Comprehensive system statistics
5. **Recommendations**: AI-powered improvement suggestions

### Quality Information Display

- **Quality Score**: Percentage score (0-100%)
- **Quality Grade**: Letter grade (A+ to F)
- **Cache Hit Rate**: Performance indicator
- **Recommendations**: Specific improvement suggestions

## Performance Optimization

### Caching Strategy

1. **Intelligent TTL**: Quality-based cache expiration
2. **Source-Based Scoring**: Higher quality sources get longer cache times
3. **Usage-Based Extension**: Frequently accessed items stay cached longer
4. **Quality Threshold**: Low-quality translations expire faster

### Memory Management

1. **Translation Memory**: Learns from corrections and feedback
2. **Similar Translation Lookup**: Context-aware translation suggestions
3. **Quality Scoring**: Automatic assessment and improvement
4. **Data Export**: Regular backup capabilities

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **Cache Issues**: Clear cache if experiencing problems
   ```bash
   python manage.py shell
   >>> from django.core.cache import cache
   >>> cache.clear()
   ```

3. **API Errors**: Check API keys in settings.py
   - Gemini API key should be valid
   - SerpAPI key is optional (system will fallback to direct Google search)

4. **Database Errors**: Run migrations
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

### Performance Issues

1. **Slow Translations**: 
   - Check internet connection for web search
   - Consider installing Redis for better caching
   - Monitor Gemini API quota

2. **High Memory Usage**:
   - Adjust cache size in settings.py
   - Clear low-quality cache regularly
   - Export and archive old translation data

## Production Deployment

### Recommended Settings

1. **Use Redis for Caching**:
   ```python
   CACHES = {
       'default': {
           'BACKEND': 'django_redis.cache.RedisCache',
           'LOCATION': 'redis://127.0.0.1:6379/1',
           'OPTIONS': {
               'CLIENT_CLASS': 'django_redis.client.DefaultClient',
           }
       }
   }
   ```

2. **Environment Variables**:
   ```bash
   export GEMINI_API_KEY="your-api-key"
   export SERPAPI_KEY="your-serpapi-key"
   export DJANGO_SECRET_KEY="your-secret-key"
   ```

3. **Database**: Consider PostgreSQL for production
4. **Static Files**: Use WhiteNoise or cloud storage
5. **Monitoring**: Set up logging and error tracking

## Support and Maintenance

### Regular Maintenance Tasks

1. **Clear Low-Quality Cache**: Weekly
2. **Export Translation Data**: Monthly backup
3. **Review Statistics**: Monitor performance trends
4. **Update Dependencies**: Keep packages current

### Monitoring

- Check translation quality trends
- Monitor cache hit rates
- Review user feedback and corrections
- Track API usage and costs

## Next Steps

1. **Test the System**: Try various translations and features
2. **Import Your Data**: Use the import rules feature for existing translations
3. **Monitor Performance**: Check statistics regularly
4. **Gather Feedback**: Use the correction system to improve translations
5. **Optimize Settings**: Adjust cache and quality thresholds based on usage

Your translation system is now professional-grade with intelligent caching, web search integration, quality scoring, and comprehensive performance monitoring!
