{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\translation_project\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\translation_project\\translation_app\\services.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:translation_app\\services.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\translation_project\\translation_app\\tests.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:translation_app\\tests.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\translation_project\\translation_app\\models.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:translation_app\\models.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\translation_project\\translation_app\\admin.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:translation_app\\admin.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\translation_project\\translation_project\\settings.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:translation_project\\settings.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\translation_project\\translation_app\\templates\\translation_app\\translate.html||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:translation_app\\templates\\translation_app\\translate.html||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\translation_project\\translation_app\\views.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:translation_app\\views.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\translation_project\\translation_app\\manage.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:translation_app\\manage.py||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1a46fd64-28d5-0019-8eb3-17a02d419b53}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "tests.py", "DocumentMoniker": "C:\\Users\\<USER>\\translation_project\\translation_app\\tests.py", "RelativeDocumentMoniker": "translation_app\\tests.py", "ToolTip": "C:\\Users\\<USER>\\translation_project\\translation_app\\tests.py", "RelativeToolTip": "translation_app\\tests.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-03T13:22:25.535Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "admin.py", "DocumentMoniker": "C:\\Users\\<USER>\\translation_project\\translation_app\\admin.py", "RelativeDocumentMoniker": "translation_app\\admin.py", "ToolTip": "C:\\Users\\<USER>\\translation_project\\translation_app\\admin.py", "RelativeToolTip": "translation_app\\admin.py", "ViewState": "AgIAAAAAAAAAAAAAAADwvxYAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-03T13:22:14.152Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "settings.py", "DocumentMoniker": "C:\\Users\\<USER>\\translation_project\\translation_project\\settings.py", "RelativeDocumentMoniker": "translation_project\\settings.py", "ToolTip": "C:\\Users\\<USER>\\translation_project\\translation_project\\settings.py", "RelativeToolTip": "translation_project\\settings.py", "ViewState": "AgIAABUAAAAAAAAAAAAAACkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-03T13:21:53.52Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "translate.html", "DocumentMoniker": "C:\\Users\\<USER>\\translation_project\\translation_app\\templates\\translation_app\\translate.html", "RelativeDocumentMoniker": "translation_app\\templates\\translation_app\\translate.html", "ToolTip": "C:\\Users\\<USER>\\translation_project\\translation_app\\templates\\translation_app\\translate.html", "RelativeToolTip": "translation_app\\templates\\translation_app\\translate.html", "ViewState": "AgIAAFMAAAAAAAAAAAAQwFoAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001512|", "WhenOpened": "2025-06-03T13:21:32.598Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "views.py", "DocumentMoniker": "C:\\Users\\<USER>\\translation_project\\translation_app\\views.py", "RelativeDocumentMoniker": "translation_app\\views.py", "ToolTip": "C:\\Users\\<USER>\\translation_project\\translation_app\\views.py", "RelativeToolTip": "translation_app\\views.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAACQAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-03T13:20:34.565Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "services.py", "DocumentMoniker": "C:\\Users\\<USER>\\translation_project\\translation_app\\services.py", "RelativeDocumentMoniker": "translation_app\\services.py", "ToolTip": "C:\\Users\\<USER>\\translation_project\\translation_app\\services.py", "RelativeToolTip": "translation_app\\services.py", "ViewState": "AgIAAPgIAAAAAAAAAABsPbkGAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-03T13:20:20.435Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "models.py", "DocumentMoniker": "C:\\Users\\<USER>\\translation_project\\translation_app\\models.py", "RelativeDocumentMoniker": "translation_app\\models.py", "ToolTip": "C:\\Users\\<USER>\\translation_project\\translation_app\\models.py", "RelativeToolTip": "translation_app\\models.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-03T13:19:57.797Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "manage.py", "DocumentMoniker": "C:\\Users\\<USER>\\translation_project\\translation_app\\manage.py", "RelativeDocumentMoniker": "translation_app\\manage.py", "ToolTip": "C:\\Users\\<USER>\\translation_project\\translation_app\\manage.py", "RelativeToolTip": "translation_app\\manage.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-06-03T13:19:38.532Z"}]}]}]}