﻿from django.db import models

class TranslationRule(models.Model):
    source_language = models.Char<PERSON>ield(max_length=20)  # 'teduray' or 'tagalog'
    source_text = models.CharField(max_length=255)
    target_text = models.Char<PERSON>ield(max_length=255)

    def __str__(self):
        return f"{self.source_language}: {self.source_text} → {self.target_text}"

class TranslationHistory(models.Model):
    source_language = models.CharField(max_length=20)
    source_text = models.TextField()
    translated_text = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.source_language}: {self.source_text[:30]}"

class TranslationCorrection(models.Model):
    """Model to store user corrections and feedback for improving translations"""
    source_language = models.CharField(max_length=20)
    target_language = models.CharField(max_length=20)
    source_text = models.TextField()
    original_translation = models.TextField()  # What the system originally provided
    corrected_translation = models.TextField()  # What the user corrected it to
    correction_type = models.Char<PERSON>ield(max_length=20, choices=[
        ('correction', 'User Correction'),
        ('addition', 'New Translation'),
        ('improvement', 'Improvement Suggestion')
    ], default='correction')
    timestamp = models.DateTimeField(auto_now_add=True)
    is_verified = models.BooleanField(default=False)  # For admin verification

    def __str__(self):
        return f"{self.source_language}→{self.target_language}: {self.source_text[:30]} (corrected)"

class TranslationFeedback(models.Model):
    """Model to store user feedback on translation quality"""
    source_language = models.CharField(max_length=20)
    target_language = models.CharField(max_length=20)
    source_text = models.TextField()
    translated_text = models.TextField()
    feedback_type = models.CharField(max_length=20, choices=[
        ('good', 'Good Translation'),
        ('bad', 'Bad Translation'),
        ('partial', 'Partially Correct')
    ])
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.feedback_type}: {self.source_text[:30]}"