#!/usr/bin/env python
"""
Test the enhanced system with training data patterns
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_enhanced_fast_replacement():
    """Test the enhanced fast replacement with training data patterns"""
    print("⚡ Testing Enhanced Fast Replacement")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test cases using words from training data
        test_cases = [
            {
                'input': "Hindi siya gumalaw.",
                'expected_improvements': ['hindi→ënda', 'siya→de'],
                'description': "Training data example 1"
            },
            {
                'input': "Ang kanyang mga mata",
                'expected_improvements': ['ang→dëb', 'kanyang→kaane', 'mga→de'],
                'description': "Training data vocabulary"
            },
            {
                'input': "Sa kanyang ina",
                'expected_improvements': ['sa→dëb', 'kanyang→kaane'],
                'description': "Possessive with location"
            },
            {
                'input': "Niya ang mga mata",
                'expected_improvements': ['niya→noy', 'ang→dëb', 'mga→de'],
                'description': "Multiple training patterns"
            },
            {
                'input': "Hindi ko alam ang buhay",
                'expected_improvements': ['hindi→ënda', 'ko→gu', 'ang→dëb'],
                'description': "Mixed original + training patterns"
            }
        ]
        
        print("🔍 Testing enhanced fast replacement:")
        
        successful_tests = 0
        for test_case in test_cases:
            input_text = test_case['input']
            expected_improvements = test_case['expected_improvements']
            description = test_case['description']
            
            print(f"\n📝 {description}")
            print(f"   Input: '{input_text}'")
            
            # Test fast replacement directly
            result = service._fast_authentic_word_replacement(input_text, "tagalog", "teduray")
            
            if result:
                print(f"   Result: '{result}'")
                
                # Check for expected improvements
                improvements_found = 0
                for expected in expected_improvements:
                    if '→' in expected:
                        old_word, new_word = expected.split('→')
                        if new_word.lower() in result.lower() and old_word.lower() not in result.lower():
                            print(f"     ✅ {expected}")
                            improvements_found += 1
                        elif new_word.lower() in result.lower():
                            print(f"     ⚠️ {expected} (partial)")
                            improvements_found += 0.5
                        else:
                            print(f"     ❌ {expected}")
                
                success_ratio = improvements_found / len(expected_improvements)
                if success_ratio >= 0.8:
                    print(f"   ✅ EXCELLENT ({success_ratio:.1%})")
                    successful_tests += 1
                elif success_ratio >= 0.5:
                    print(f"   ✅ GOOD ({success_ratio:.1%})")
                    successful_tests += 0.8
                else:
                    print(f"   ⚠️ NEEDS WORK ({success_ratio:.1%})")
                    successful_tests += 0.3
            else:
                print(f"   ❌ No result from fast replacement")
        
        total_tests = len(test_cases)
        overall_success = successful_tests / total_tests
        
        print(f"\n📊 Enhanced fast replacement: {overall_success:.1%} success rate")
        
        if overall_success >= 0.8:
            print("🎉 EXCELLENT! Enhanced patterns working great!")
            return True
        elif overall_success >= 0.6:
            print("✅ GOOD! Enhanced patterns working well!")
            return True
        else:
            print("⚠️ Enhanced patterns need more work")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced fast replacement test error: {e}")
        return False

def test_training_data_translations():
    """Test translations using exact training data examples"""
    print("\n📚 Testing Training Data Translations")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test with exact training examples
        training_examples = [
            ("Hindi siya gumalaw.", "Da mënlëkuto no."),
            ("Muli siyang lumingon", "Sëmnëling man"),
            ("Para tumakas.", "Inok mërarëy."),
            ("aakyat na ako.", "Mënik kun.")
        ]
        
        print("🔍 Testing with training examples:")
        
        successful_translations = 0
        for tagalog, expected_teduray in training_examples:
            print(f"\n📝 Testing: '{tagalog}'")
            print(f"   Expected: '{expected_teduray}'")
            
            result = service.translate(tagalog, "tagalog", "teduray")
            print(f"   Got: '{result}'")
            
            # Check similarity (exact match or contains key elements)
            if result.lower() == expected_teduray.lower():
                print(f"   ✅ PERFECT MATCH!")
                successful_translations += 1
            elif any(word in result.lower() for word in expected_teduray.lower().split() if len(word) > 2):
                print(f"   ✅ GOOD (contains key elements)")
                successful_translations += 0.8
            elif result.lower() != tagalog.lower():
                print(f"   ⚠️ TRANSLATED (but different from expected)")
                successful_translations += 0.5
            else:
                print(f"   ❌ NO TRANSLATION")
        
        total_examples = len(training_examples)
        success_rate = successful_translations / total_examples
        
        print(f"\n📊 Training data translation success: {success_rate:.1%}")
        
        if success_rate >= 0.8:
            print("🎉 EXCELLENT! Training data working perfectly!")
            return True
        elif success_rate >= 0.6:
            print("✅ GOOD! Training data mostly working!")
            return True
        else:
            print("⚠️ Training data translations need improvement")
            return False
            
    except Exception as e:
        print(f"❌ Training data translation test error: {e}")
        return False

def test_problematic_sentence_improvement():
    """Test if the problematic sentence is now better"""
    print("\n🔧 Testing Problematic Sentence Improvement")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # The original problematic sentence
        problematic_text = "ang buhay ko pala ay wala ng halaga"
        
        print(f"🔍 Testing improved translation of: '{problematic_text}'")
        
        result = service.translate(problematic_text, "tagalog", "teduray")
        print(f"📤 Result: '{result}'")
        
        # Check for improvements with new training patterns
        improvements = []
        
        # Check training data patterns
        if 'dëb' in result and 'ang' not in result:
            improvements.append("✅ 'ang' → 'dëb' (training pattern)")
        elif 'dëb' in result:
            improvements.append("⚠️ Has 'dëb' but may still have 'ang'")
        else:
            improvements.append("❌ Still uses 'ang' instead of 'dëb'")
        
        if 'gu' in result and 'ko' not in result:
            improvements.append("✅ 'ko' → 'gu' (original pattern)")
        elif 'gu' in result:
            improvements.append("⚠️ Has 'gu' but may still have 'ko'")
        else:
            improvements.append("❌ Still uses 'ko'")
        
        if 'ënda' in result and 'wala' not in result:
            improvements.append("✅ 'wala' → 'ënda' (training pattern)")
        elif 'ënda' in result:
            improvements.append("⚠️ Has 'ënda' but may still have 'wala'")
        else:
            improvements.append("❌ Still uses 'wala'")
        
        print(f"\n📊 Improvements with training data:")
        for improvement in improvements:
            print(f"   {improvement}")
        
        # Count successful improvements
        successful = sum(1 for imp in improvements if imp.startswith("✅"))
        partial = sum(1 for imp in improvements if imp.startswith("⚠️"))
        total = len(improvements)
        
        improvement_score = (successful + partial * 0.5) / total
        
        print(f"\n📈 Improvement score: {improvement_score:.1%}")
        
        if improvement_score >= 0.8:
            print("🎉 EXCELLENT! Problematic sentence greatly improved!")
            return True
        elif improvement_score >= 0.6:
            print("✅ GOOD! Problematic sentence improved!")
            return True
        else:
            print("⚠️ Problematic sentence still needs work")
            return False
            
    except Exception as e:
        print(f"❌ Problematic sentence test error: {e}")
        return False

def test_learning_with_training_patterns():
    """Test if learning system works with training patterns"""
    print("\n🧠 Testing Learning with Training Patterns")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationRule
        
        service = TranslationService()
        
        # Test learning with new sentence using training patterns
        test_sentence = "Hindi siya nakita ang mga mata"
        
        print(f"🔍 Testing learning with: '{test_sentence}'")
        
        # Count initial rules
        initial_count = TranslationRule.objects.filter(source_language='tagalog').count()
        print(f"📊 Initial rules: {initial_count}")
        
        # Translate (should use training patterns + learn new words)
        result = service.translate(test_sentence, "tagalog", "teduray")
        print(f"📤 Translation: '{result}'")
        
        # Count final rules
        final_count = TranslationRule.objects.filter(source_language='tagalog').count()
        print(f"📊 Final rules: {final_count}")
        
        # Check if training patterns were used
        training_patterns_used = 0
        if 'ënda' in result:  # hindi → ënda
            training_patterns_used += 1
            print(f"   ✅ Used training pattern: hindi → ënda")
        if 'de' in result:    # siya → de
            training_patterns_used += 1
            print(f"   ✅ Used training pattern: siya → de")
        if 'dëb' in result:   # ang → dëb, mga → de
            training_patterns_used += 1
            print(f"   ✅ Used training pattern: ang/mga → dëb/de")
        
        # Check if learning occurred
        learning_occurred = final_count > initial_count
        if learning_occurred:
            print(f"   ✅ Learning occurred: +{final_count - initial_count} rules")
        else:
            print(f"   ℹ️ No new rules (may already exist)")
        
        # Overall assessment
        if training_patterns_used >= 2 or learning_occurred:
            print(f"\n🎉 LEARNING WITH TRAINING PATTERNS WORKING!")
            return True
        elif training_patterns_used >= 1:
            print(f"\n✅ PARTIAL SUCCESS with training patterns")
            return True
        else:
            print(f"\n⚠️ Training patterns not being used effectively")
            return False
            
    except Exception as e:
        print(f"❌ Learning with training patterns test error: {e}")
        return False

if __name__ == "__main__":
    print("🎓 Enhanced Training System Test")
    print("=" * 70)
    print("Testing system with integrated training data patterns")
    print("=" * 70)
    
    # Run enhanced tests
    fast_replacement = test_enhanced_fast_replacement()
    training_translations = test_training_data_translations()
    problematic_improvement = test_problematic_sentence_improvement()
    learning_patterns = test_learning_with_training_patterns()
    
    print("\n" + "=" * 70)
    print("📊 ENHANCED TRAINING RESULTS")
    print("=" * 70)
    
    results = {
        "Enhanced Fast Replacement": fast_replacement,
        "Training Data Translations": training_translations,
        "Problematic Sentence Fix": problematic_improvement,
        "Learning with Training Patterns": learning_patterns
    }
    
    for test_name, result in results.items():
        status = "✅ WORKING" if result else "❌ NEEDS WORK"
        print(f"{test_name}: {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    enhancement_score = (working_count / total_count) * 100
    
    print(f"\n📈 Enhancement Score: {enhancement_score:.1f}%")
    
    if working_count == total_count:
        print(f"\n🎉 PERFECT ENHANCEMENT!")
        print("Training data integration is 100% successful!")
        print("\n💡 Enhanced Features:")
        print("  • ✅ Training data patterns integrated")
        print("  • ✅ Native speaker vocabulary active")
        print("  • ✅ Improved fast replacement")
        print("  • ✅ Better translation quality")
        print("  • ✅ Learning system enhanced")
        print("\n🚀 Your system now uses:")
        print("  • Authentic native speaker patterns")
        print("  • Enhanced vocabulary from training data")
        print("  • Improved grammatical structures")
        print("  • Better cultural context")
        print("  • Professional translation quality")
    elif enhancement_score >= 75:
        print(f"\n🎯 EXCELLENT! {enhancement_score:.1f}% enhancement achieved!")
        print("Training data integration is highly successful")
    elif enhancement_score >= 50:
        print(f"\n✅ GOOD! {enhancement_score:.1f}% enhancement achieved!")
        print("Training data integration is working well")
    else:
        print(f"\n⚠️ {enhancement_score:.1f}% enhancement - needs more work")
        print("Training data integration needs improvement")
    
    print(f"\n🎓 Training Integration: {'COMPLETE' if enhancement_score >= 75 else 'IN PROGRESS'}")
    print("=" * 70)
