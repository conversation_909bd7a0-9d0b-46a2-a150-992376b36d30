#!/usr/bin/env python
"""
Test the fast ChatGPT-style translation system
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_fast_chatgpt_style_translation():
    """Test the fast ChatGPT-style translation system"""
    print("⚡ Testing Fast ChatGPT-Style Translation System")
    print("=" * 70)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test cases that should trigger the fast translation system
        test_cases = [
            {
                'input': "Ang puso ko at anak ko ay iisa ng nararamdaman.",
                'description': "Complex emotional sentence (like ChatGPT example)",
                'expected_elements': ["ënga", "gu"]  # Should use authentic child and possessive
            },
            {
                'input': "Kumain ang bata ng tinapay.",
                'description': "Simple action sentence",
                'expected_elements': ["ënga", "menama"]  # child, eat
            },
            {
                'input': "May dalawang anak ako.",
                'description': "Possession with number",
                'expected_elements': ["ruwo", "ënga", "gu"]  # two, child, my
            },
            {
                'input': "Hindi ko alam kung saan siya.",
                'description': "Complex sentence with negation",
                'expected_elements': ["ënda", "gëtuwa", "gu"]  # I don't know pattern
            },
            {
                'input': "Masaya ang pamilya namin.",
                'description': "Family happiness statement",
                'expected_elements': ["mefiya"]  # happy
            },
            {
                'input': "Bukas tayo magkikita.",
                'description': "Future meeting plan",
                'expected_elements': ["sëgito", "dëmo"]  # tomorrow, we
            }
        ]
        
        print("🔍 Testing fast ChatGPT-style translations:")
        
        successful_tests = 0
        for test_case in test_cases:
            input_text = test_case['input']
            description = test_case['description']
            expected_elements = test_case['expected_elements']
            
            print(f"\n📝 {description}")
            print(f"   Input: '{input_text}'")
            
            # Get translation using the fast system
            result = service.translate(input_text, "tagalog", "teduray")
            print(f"   Result: '{result}'")
            
            # Check if result contains expected authentic elements
            result_lower = result.lower()
            found_elements = []
            for element in expected_elements:
                if element.lower() in result_lower:
                    found_elements.append(element)
            
            match_ratio = len(found_elements) / len(expected_elements) if expected_elements else 0
            
            if match_ratio >= 0.5:
                print(f"   ✅ GOOD ({match_ratio:.1%} authentic elements: {found_elements})")
                successful_tests += 1
            elif match_ratio > 0:
                print(f"   ⚠️  PARTIAL ({match_ratio:.1%} authentic elements: {found_elements})")
                successful_tests += 0.5
            else:
                print(f"   ❌ POOR (no authentic elements found)")
                
            # Check if translation is different from input (not just copying)
            if result.lower() != input_text.lower():
                print(f"   ✅ Translation attempted (not just copying)")
            else:
                print(f"   ⚠️  May be copying input")
        
        total_tests = len(test_cases)
        print(f"\n📊 Successful tests: {successful_tests}/{total_tests}")
        
        if successful_tests >= total_tests * 0.7:
            print("✅ Fast ChatGPT-style translation system working!")
            return True
        else:
            print("⚠️  Fast translation system needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Fast translation test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_systematic_approach():
    """Test the systematic approach components"""
    print("\n🔬 Testing Systematic Approach Components")
    print("=" * 70)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test the individual components
        test_text = "May mga anak ka ba?"
        
        print(f"🔍 Testing systematic components for: '{test_text}'")
        
        # Test meaning analysis
        meaning = service._analyze_meaning_and_context(test_text, "tagalog")
        print(f"   📊 Meaning Analysis: {meaning}")
        
        # Test key concepts identification
        concepts = service._identify_key_concepts(test_text, "tagalog")
        print(f"   🔑 Key Concepts:\n{concepts}")
        
        # Test linguistic patterns
        patterns = service._get_teduray_linguistic_patterns()
        print(f"   📚 Linguistic Patterns: {patterns[:100]}...")
        
        # Check if components are working
        components_working = 0
        
        if meaning and len(meaning) > 10:
            print(f"   ✅ Meaning analysis working")
            components_working += 1
        else:
            print(f"   ❌ Meaning analysis not working")
        
        if concepts and "anak" in concepts and "ënga" in concepts:
            print(f"   ✅ Key concepts identification working")
            components_working += 1
        else:
            print(f"   ❌ Key concepts identification not working")
        
        if patterns and "TEDURAY GRAMMAR" in patterns:
            print(f"   ✅ Linguistic patterns working")
            components_working += 1
        else:
            print(f"   ❌ Linguistic patterns not working")
        
        print(f"\n📊 Working components: {components_working}/3")
        
        if components_working >= 2:
            print("✅ Systematic approach components working!")
            return True
        else:
            print("⚠️  Systematic approach needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Systematic approach test error: {e}")
        return False

def test_speed_comparison():
    """Test speed of different translation methods"""
    print("\n⏱️  Testing Translation Speed Comparison")
    print("=" * 70)
    
    try:
        from translation_app.services import TranslationService
        import time
        
        service = TranslationService()
        
        test_sentences = [
            "May anak ka ba?",
            "Kumain ang bata.",
            "Mahal ko ang pamilya ko.",
            "Hindi ko alam."
        ]
        
        print("🔍 Testing translation speed:")
        
        total_time = 0
        successful_translations = 0
        
        for sentence in test_sentences:
            print(f"\n📝 Translating: '{sentence}'")
            
            start_time = time.time()
            result = service.translate(sentence, "tagalog", "teduray")
            end_time = time.time()
            
            translation_time = end_time - start_time
            total_time += translation_time
            
            print(f"   Result: '{result}'")
            print(f"   Time: {translation_time:.2f} seconds")
            
            if result and result != sentence:
                successful_translations += 1
                print(f"   ✅ Translation successful")
            else:
                print(f"   ⚠️  Translation may have failed")
        
        average_time = total_time / len(test_sentences)
        success_rate = successful_translations / len(test_sentences)
        
        print(f"\n📊 Speed Results:")
        print(f"   Total time: {total_time:.2f} seconds")
        print(f"   Average time per translation: {average_time:.2f} seconds")
        print(f"   Success rate: {success_rate:.1%}")
        
        # Consider it fast if average time is under 10 seconds and success rate > 70%
        if average_time < 10 and success_rate >= 0.7:
            print("✅ Translation system is fast and effective!")
            return True
        elif success_rate >= 0.7:
            print("✅ Translation system is effective but could be faster")
            return True
        else:
            print("⚠️  Translation system needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ Speed test error: {e}")
        return False

def test_chatgpt_methodology():
    """Test if our system follows ChatGPT's methodology"""
    print("\n🤖 Testing ChatGPT Methodology Implementation")
    print("=" * 70)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test with a complex sentence like ChatGPT's example
        complex_sentence = "Ang puso ko at anak ko ay iisa ng nararamdaman."
        
        print(f"🔍 Testing ChatGPT methodology with: '{complex_sentence}'")
        
        # This should trigger our fast ChatGPT-style translation
        result = service._fast_chatgpt_style_translation(complex_sentence, "tagalog", "teduray")
        
        if result:
            print(f"   📤 Fast ChatGPT-style result: '{result}'")
            
            # Check if it follows the methodology
            methodology_checks = 0
            
            # Check 1: Uses authentic vocabulary
            if "ënga" in result.lower():  # Should use authentic "child"
                print(f"   ✅ Uses authentic vocabulary (ënga for child)")
                methodology_checks += 1
            else:
                print(f"   ⚠️  May not use authentic vocabulary")
            
            # Check 2: Different from input (shows translation)
            if result.lower() != complex_sentence.lower():
                print(f"   ✅ Provides actual translation")
                methodology_checks += 1
            else:
                print(f"   ❌ May be copying input")
            
            # Check 3: Reasonable length (not too short or too long)
            if 5 <= len(result.split()) <= 15:
                print(f"   ✅ Reasonable translation length")
                methodology_checks += 1
            else:
                print(f"   ⚠️  Translation length may be off")
            
            print(f"\n📊 Methodology checks passed: {methodology_checks}/3")
            
            if methodology_checks >= 2:
                print("✅ ChatGPT methodology implemented successfully!")
                return True
            else:
                print("⚠️  ChatGPT methodology needs refinement")
                return False
        else:
            print(f"   ❌ Fast ChatGPT-style translation failed")
            return False
            
    except Exception as e:
        print(f"❌ ChatGPT methodology test error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Fast ChatGPT-Style Translation System Test")
    print("=" * 80)
    print("Testing the systematic approach inspired by ChatGPT's methodology")
    print("=" * 80)
    
    # Run tests
    fast_translation_ok = test_fast_chatgpt_style_translation()
    systematic_ok = test_systematic_approach()
    speed_ok = test_speed_comparison()
    methodology_ok = test_chatgpt_methodology()
    
    print("\n" + "=" * 80)
    print("📊 FINAL RESULTS")
    print("=" * 80)
    
    results = {
        "Fast ChatGPT-Style Translation": fast_translation_ok,
        "Systematic Approach": systematic_ok,
        "Speed Performance": speed_ok,
        "ChatGPT Methodology": methodology_ok
    }
    
    for test_name, result in results.items():
        status = "✅ WORKING" if result else "❌ NEEDS WORK"
        print(f"{test_name}: {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    
    if working_count == total_count:
        print(f"\n🎉 ALL {total_count} SYSTEMS WORKING!")
        print("The fast ChatGPT-style translation system is ready!")
        print("\n💡 Key Features Implemented:")
        print("  • ✅ Systematic meaning analysis")
        print("  • ✅ Key concept identification")
        print("  • ✅ Linguistic pattern application")
        print("  • ✅ Grammar rule combination")
        print("  • ✅ Fast translation processing")
        print("\n🚀 Your translation system now uses ChatGPT's methodology:")
        print("  • Step 1: Understand meaning")
        print("  • Step 2: Identify key concepts")
        print("  • Step 3: Apply linguistic patterns")
        print("  • Step 4: Combine with grammar rules")
        print("  • Step 5: Generate authentic translation")
    else:
        print(f"\n⚠️  {working_count}/{total_count} systems working")
        print("The system is functional but may need optimization")
    
    print(f"\n⚡ Fast, systematic translations using Gemini AI are now available!")
    print("=" * 80)
