"""
Translation Quality Scorer
Provides intelligent quality assessment for translations using multiple criteria.
"""

import re
import math
from collections import Counter
from django.db.models import Avg
from .models import TranslationRule, TranslationFeedback, TranslationCorrection


class TranslationQualityScorer:
    """Advanced quality scoring system for translations"""
    
    def __init__(self):
        self.weights = {
            'source_authority': 0.25,    # How authoritative is the source
            'linguistic_quality': 0.20,  # Grammar, structure, etc.
            'consistency': 0.15,         # Consistency with existing translations
            'user_feedback': 0.15,       # Historical user feedback
            'length_appropriateness': 0.10,  # Appropriate length ratio
            'completeness': 0.10,        # All words translated
            'cultural_appropriateness': 0.05  # Cultural context
        }
    
    def score_translation(self, original_text, translated_text, source_lang, target_lang, 
                         translation_source="unknown", context=None):
        """
        Calculate comprehensive quality score for a translation
        
        Args:
            original_text: Original text to translate
            translated_text: The translation
            source_lang: Source language
            target_lang: Target language
            translation_source: Source of translation (gemini, translate_py, bible, etc.)
            context: Additional context information
        
        Returns:
            Dictionary with overall score and component scores
        """
        scores = {}
        
        # Calculate individual component scores
        scores['source_authority'] = self._score_source_authority(translation_source)
        scores['linguistic_quality'] = self._score_linguistic_quality(
            original_text, translated_text, source_lang, target_lang
        )
        scores['consistency'] = self._score_consistency(
            original_text, translated_text, source_lang, target_lang
        )
        scores['user_feedback'] = self._score_user_feedback(
            original_text, translated_text, source_lang, target_lang
        )
        scores['length_appropriateness'] = self._score_length_appropriateness(
            original_text, translated_text
        )
        scores['completeness'] = self._score_completeness(
            original_text, translated_text, source_lang
        )
        scores['cultural_appropriateness'] = self._score_cultural_appropriateness(
            original_text, translated_text, target_lang
        )
        
        # Calculate weighted overall score
        overall_score = sum(
            scores[component] * self.weights[component] 
            for component in self.weights
        )
        
        return {
            'overall_score': round(overall_score, 3),
            'component_scores': scores,
            'grade': self._get_quality_grade(overall_score),
            'recommendations': self._get_recommendations(scores)
        }
    
    def _score_source_authority(self, source):
        """Score based on the authority/reliability of the translation source"""
        authority_scores = {
            'translate_py': 0.95,      # High authority - curated data
            'bible_corpus': 0.90,      # High authority - religious text
            'user_correction': 0.85,   # High authority - human verified
            'database_exact': 0.80,    # Good authority - stored rules
            'gemini_enhanced': 0.70,   # Good authority - AI with context
            'gemini_basic': 0.60,      # Medium authority - basic AI
            'web_search': 0.50,        # Medium authority - web sources
            'dictionary': 0.45,        # Lower authority - word-by-word
            'unknown': 0.30            # Low authority - unknown source
        }
        
        return authority_scores.get(source, 0.30)
    
    def _score_linguistic_quality(self, original, translated, source_lang, target_lang):
        """Score linguistic quality based on grammar and structure"""
        score = 0.5  # Base score
        
        # Check for proper sentence structure
        if self._has_proper_sentence_structure(translated, target_lang):
            score += 0.2
        
        # Check for appropriate punctuation preservation
        if self._preserves_punctuation_appropriately(original, translated):
            score += 0.1
        
        # Check for word order appropriateness
        if self._has_appropriate_word_order(translated, target_lang):
            score += 0.1
        
        # Check for grammatical markers (for Teduray)
        if target_lang.lower() == 'teduray':
            if self._has_teduray_grammatical_markers(translated):
                score += 0.1
        
        return min(score, 1.0)
    
    def _score_consistency(self, original, translated, source_lang, target_lang):
        """Score consistency with existing translations in database"""
        try:
            # Find similar translations in database
            similar_rules = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__icontains=original[:20]  # Partial match
            )
            
            if not similar_rules.exists():
                return 0.5  # Neutral score if no similar translations
            
            consistency_score = 0.0
            total_comparisons = 0
            
            for rule in similar_rules[:10]:  # Limit to 10 comparisons
                # Calculate similarity between translations
                similarity = self._calculate_text_similarity(translated, rule.target_text)
                consistency_score += similarity
                total_comparisons += 1
            
            return consistency_score / total_comparisons if total_comparisons > 0 else 0.5
            
        except Exception:
            return 0.5
    
    def _score_user_feedback(self, original, translated, source_lang, target_lang):
        """Score based on historical user feedback for similar translations"""
        try:
            # Look for feedback on similar translations
            feedback = TranslationFeedback.objects.filter(
                source_language=source_lang,
                target_language=target_lang,
                source_text__icontains=original[:15]
            )
            
            if not feedback.exists():
                return 0.5  # Neutral score if no feedback
            
            # Convert feedback to scores
            feedback_scores = {
                'good': 1.0,
                'partial': 0.5,
                'bad': 0.0
            }
            
            scores = [feedback_scores.get(f.feedback_type, 0.5) for f in feedback]
            return sum(scores) / len(scores)
            
        except Exception:
            return 0.5
    
    def _score_length_appropriateness(self, original, translated):
        """Score based on appropriate length ratio between original and translation"""
        orig_words = len(original.split())
        trans_words = len(translated.split())
        
        if orig_words == 0:
            return 0.0
        
        ratio = trans_words / orig_words
        
        # Ideal ratio is between 0.5 and 2.0
        if 0.8 <= ratio <= 1.5:
            return 1.0
        elif 0.5 <= ratio <= 2.0:
            return 0.8
        elif 0.3 <= ratio <= 3.0:
            return 0.5
        else:
            return 0.2
    
    def _score_completeness(self, original, translated, source_lang):
        """Score based on how completely the text was translated"""
        # Check if translation contains untranslated words from original
        orig_words = set(original.lower().split())
        trans_words = set(translated.lower().split())
        
        # Common words that don't need translation
        common_words = {
            'tagalog': {'ang', 'ng', 'sa', 'na', 'at', 'ay', 'ko', 'mo', 'ka'},
            'english': {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at'},
            'teduray': {'i', 'de', 'go', 'nu', 'ku', 'beem', 'been'}
        }
        
        source_common = common_words.get(source_lang.lower(), set())
        
        # Find untranslated content words
        untranslated = orig_words & trans_words - source_common
        
        if len(orig_words - source_common) == 0:
            return 1.0
        
        completeness_ratio = 1.0 - (len(untranslated) / len(orig_words - source_common))
        return max(completeness_ratio, 0.0)
    
    def _score_cultural_appropriateness(self, original, translated, target_lang):
        """Score cultural appropriateness of translation"""
        score = 0.5  # Base score
        
        if target_lang.lower() == 'teduray':
            # Check for culturally appropriate terms
            cultural_indicators = [
                'begen', 'beem', 'been',  # Proper pronouns
                'fiyoy', 'fiyo',          # Positive expressions
                'tobong', 'galb�k',       # Cultural concepts
            ]
            
            for indicator in cultural_indicators:
                if indicator in translated.lower():
                    score += 0.1
        
        return min(score, 1.0)
    
    def _has_proper_sentence_structure(self, text, language):
        """Check if text has proper sentence structure for the language"""
        # Basic checks for sentence structure
        if not text or len(text.strip()) == 0:
            return False
        
        # Check for reasonable word count
        words = text.split()
        if len(words) < 1:
            return False
        
        # Language-specific checks
        if language.lower() == 'teduray':
            # Teduray often has specific patterns
            return True  # Placeholder - would need linguistic rules
        
        return True
    
    def _preserves_punctuation_appropriately(self, original, translated):
        """Check if punctuation is preserved appropriately"""
        orig_punct = re.findall(r'[.!?]', original)
        trans_punct = re.findall(r'[.!?]', translated)
        
        # Should have similar punctuation
        return len(orig_punct) == len(trans_punct)
    
    def _has_appropriate_word_order(self, text, language):
        """Check if word order is appropriate for the language"""
        # This would require sophisticated linguistic analysis
        # For now, just check basic patterns
        return len(text.split()) > 0
    
    def _has_teduray_grammatical_markers(self, text):
        """Check for proper Teduray grammatical markers"""
        teduray_markers = ['i', 'de', 'go', 'nu', 'ku', 'beem', 'been', 'ro', 'gey']
        text_lower = text.lower()
        
        marker_count = sum(1 for marker in teduray_markers if marker in text_lower)
        return marker_count > 0
    
    def _calculate_text_similarity(self, text1, text2):
        """Calculate similarity between two texts"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = words1 & words2
        union = words1 | words2
        
        return len(intersection) / len(union)
    
    def _get_quality_grade(self, score):
        """Convert numeric score to letter grade"""
        if score >= 0.9:
            return 'A+'
        elif score >= 0.8:
            return 'A'
        elif score >= 0.7:
            return 'B'
        elif score >= 0.6:
            return 'C'
        elif score >= 0.5:
            return 'D'
        else:
            return 'F'
    
    def _get_recommendations(self, scores):
        """Generate recommendations based on component scores"""
        recommendations = []
        
        if scores['linguistic_quality'] < 0.6:
            recommendations.append("Consider improving grammatical structure")
        
        if scores['consistency'] < 0.5:
            recommendations.append("Check consistency with existing translations")
        
        if scores['completeness'] < 0.7:
            recommendations.append("Ensure all words are properly translated")
        
        if scores['length_appropriateness'] < 0.6:
            recommendations.append("Review translation length - may be too short or too long")
        
        if scores['cultural_appropriateness'] < 0.6:
            recommendations.append("Consider cultural context and appropriateness")
        
        return recommendations
