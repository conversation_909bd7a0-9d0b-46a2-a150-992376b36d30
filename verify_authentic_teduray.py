#!/usr/bin/env python
"""
Verify authentic Teduray translations from our actual data sources
"""

import os
import django
import re
from collections import defaultdict, Counter

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def check_actual_teduray_data():
    """Check what actual Teduray words we have in our data"""
    print("🔍 Checking Actual Teduray Data")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationRule
        
        service = TranslationService()
        
        # Check specific words you mentioned
        test_words = ['bata', 'anak', 'lalaki', 'babae', 'kumain', 'masaya']
        
        print("📊 Checking specific word translations:")
        
        for word in test_words:
            print(f"\n🔍 Word: '{word}'")
            
            # Check database
            db_rules = TranslationRule.objects.filter(
                source_language='tagalog',
                source_text__iexact=word
            )
            
            if db_rules:
                for rule in db_rules:
                    print(f"   Database: '{word}' → '{rule.target_text}'")
            else:
                print(f"   Database: No match for '{word}'")
            
            # Check translate.py
            py_matches = []
            for entry in service.translate_py_data:
                if (entry.get('source', '').lower() == word.lower() and 
                    'teduray' in str(entry.get('target_lang', '')).lower()):
                    py_matches.append(entry.get('target', ''))
            
            if py_matches:
                for match in py_matches:
                    print(f"   translate.py: '{word}' → '{match}'")
            else:
                print(f"   translate.py: No match for '{word}'")
            
            # Test our current translation system
            result = service.translate(word, "tagalog", "teduray")
            print(f"   Our system: '{word}' → '{result}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def find_authentic_patterns():
    """Find authentic Teduray patterns from our actual data"""
    print("\n🔍 Finding Authentic Teduray Patterns")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationRule
        
        service = TranslationService()
        
        # Get all actual Teduray translations
        print("📊 Scanning all Teduray data...")
        
        authentic_pairs = []
        
        # From database
        db_rules = TranslationRule.objects.filter(source_language='tagalog')
        for rule in db_rules:
            if rule.target_text and rule.target_text.strip():
                authentic_pairs.append((rule.source_text, rule.target_text))
        
        print(f"   Database: {len(authentic_pairs)} translation pairs")
        
        # From translate.py
        py_pairs = []
        for entry in service.translate_py_data:
            source = entry.get('source', '')
            target = entry.get('target', '')
            target_lang = str(entry.get('target_lang', '')).lower()
            
            if source and target and 'teduray' in target_lang:
                py_pairs.append((source, target))
        
        print(f"   translate.py: {len(py_pairs)} translation pairs")
        
        # Show some authentic examples
        print("\n📝 Authentic Translation Examples:")
        
        # Show database examples
        print("   From Database:")
        for i, (source, target) in enumerate(authentic_pairs[:10]):
            print(f"     '{source}' → '{target}'")
        
        print("\n   From translate.py:")
        for i, (source, target) in enumerate(py_pairs[:10]):
            print(f"     '{source}' → '{target}'")
        
        return authentic_pairs + py_pairs
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def analyze_real_markers(authentic_pairs):
    """Analyze markers from real Teduray translations"""
    print("\n🔤 Analyzing Real Teduray Markers")
    print("=" * 50)
    
    # Extract all Teduray text
    teduray_texts = [target for source, target in authentic_pairs if target]
    
    # Find short words that might be markers
    potential_markers = Counter()
    marker_contexts = defaultdict(list)
    
    for text in teduray_texts:
        words = text.lower().split()
        
        for i, word in enumerate(words):
            if len(word) <= 3 and word.isalpha():
                potential_markers[word] += 1
                
                # Get context
                before = words[i-1] if i > 0 else ""
                after = words[i+1] if i < len(words)-1 else ""
                marker_contexts[word].append(f"{before} [{word}] {after}")
    
    print("📊 Most Common Short Words (Potential Markers):")
    for marker, count in potential_markers.most_common(20):
        print(f"   '{marker}': {count} occurrences")
        
        # Show some contexts
        contexts = marker_contexts[marker][:3]
        for context in contexts:
            print(f"      Example: {context}")
        print()
    
    # Check our current markers
    our_markers = ['i', 'de', 'nu', 'kun', 'go']
    print("🔍 Our Current Markers vs Real Data:")
    
    for marker in our_markers:
        count = potential_markers.get(marker, 0)
        if count > 0:
            print(f"   ✅ '{marker}': {count} occurrences in real data")
        else:
            print(f"   ❌ '{marker}': NOT found in real data")
    
    return potential_markers

def check_word_corrections():
    """Check specific word corrections you mentioned"""
    print("\n🔧 Checking Word Corrections")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Words you mentioned as incorrect
        corrections_to_check = [
            ('bata', 'What should this be in Teduray?'),
            ('anak', 'What should this be in Teduray?'),
        ]
        
        print("🔍 Current translations for words you mentioned:")
        
        for tagalog_word, question in corrections_to_check:
            print(f"\n   Word: '{tagalog_word}'")
            
            # Check what our system currently produces
            current_result = service.translate(tagalog_word, "tagalog", "teduray")
            print(f"   Our current translation: '{current_result}'")
            
            # Check translate.py data
            py_matches = []
            for entry in service.translate_py_data:
                if (entry.get('source', '').lower() == tagalog_word.lower() and 
                    'teduray' in str(entry.get('target_lang', '')).lower()):
                    py_matches.append(entry.get('target', ''))
            
            if py_matches:
                print(f"   translate.py has: {py_matches}")
            else:
                print(f"   translate.py: No direct match")
            
            print(f"   Question: {question}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def search_for_correct_words():
    """Search for the correct Teduray words in our data"""
    print("\n🔍 Searching for Correct Teduray Words")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Search for words that might be the correct translations
        search_terms = ['child', 'kid', 'baby', 'son', 'daughter']
        
        print("🔍 Searching for child-related words:")
        
        for term in search_terms:
            print(f"\n   Searching for '{term}':")
            
            # Search in translate.py
            matches = []
            for entry in service.translate_py_data:
                source = entry.get('source', '').lower()
                target = entry.get('target', '')
                
                if term in source and 'teduray' in str(entry.get('target_lang', '')).lower():
                    matches.append((entry.get('source'), target))
            
            if matches:
                for source, target in matches[:5]:
                    print(f"     '{source}' → '{target}'")
            else:
                print(f"     No matches for '{term}'")
        
        # Also search for family-related terms
        print("\n🔍 Searching for family-related words:")
        family_terms = ['family', 'father', 'mother', 'parent', 'boy', 'girl']
        
        for term in family_terms:
            matches = []
            for entry in service.translate_py_data:
                source = entry.get('source', '').lower()
                target = entry.get('target', '')
                
                if term in source and 'teduray' in str(entry.get('target_lang', '')).lower():
                    matches.append((entry.get('source'), target))
            
            if matches:
                print(f"   '{term}': {matches[0][1]}")  # Show first match
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔬 Authentic Teduray Data Verification")
    print("=" * 60)
    print("Checking our actual data against real Teduray translations")
    print("=" * 60)
    
    # Step 1: Check specific words
    check_actual_teduray_data()
    
    # Step 2: Find authentic patterns
    authentic_pairs = find_authentic_patterns()
    
    # Step 3: Analyze real markers
    if authentic_pairs:
        analyze_real_markers(authentic_pairs)
    
    # Step 4: Check corrections
    check_word_corrections()
    
    # Step 5: Search for correct words
    search_for_correct_words()
    
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    print("✅ Checked actual Teduray data sources")
    print("✅ Analyzed authentic translation pairs")
    print("✅ Verified grammatical markers against real data")
    print("✅ Identified potential word correction needs")
    
    print("\n🎯 Next Steps:")
    print("1. Please provide the correct Teduray translations for 'bata' and 'anak'")
    print("2. We'll update our word mappings based on authentic data")
    print("3. We'll verify all grammatical markers against real usage")
    print("4. We'll correct any inauthentic patterns in our system")
    
    print("\n" + "=" * 60)
