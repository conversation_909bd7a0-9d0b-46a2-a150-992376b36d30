#!/usr/bin/env python
"""
Test if the learning system is actually working
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_learning_from_translations():
    """Test if the system learns from new translations"""
    print("🧠 Testing Learning from Translations")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationRule
        
        service = TranslationService()
        
        # Test with a new word that should trigger learning
        test_word = "smartphone"
        
        print(f"🔍 Testing learning with: '{test_word}'")
        
        # Check initial state
        initial_count = TranslationRule.objects.filter(
            source_language='tagalog',
            source_text__icontains=test_word
        ).count()
        
        print(f"📊 Initial rules containing '{test_word}': {initial_count}")
        
        # Translate (this should trigger learning if <PERSON> provides a translation)
        print(f"\n🔄 Translating '{test_word}'...")
        result = service.translate(test_word, "tagalog", "teduray")
        print(f"   Result: '{result}'")
        
        # Check if new rules were learned
        final_count = TranslationRule.objects.filter(
            source_language='tagalog',
            source_text__icontains=test_word
        ).count()
        
        print(f"📊 Final rules containing '{test_word}': {final_count}")
        
        if final_count > initial_count:
            print(f"   ✅ LEARNING DETECTED! Added {final_count - initial_count} new rules")
            
            # Show what was learned
            new_rules = TranslationRule.objects.filter(
                source_language='tagalog',
                source_text__icontains=test_word
            )
            
            for rule in new_rules:
                print(f"   📚 Learned: '{rule.source_text}' → '{rule.target_text}'")
            
            return True
        else:
            print(f"   ⚠️  No new rules learned")
            return False
            
    except Exception as e:
        print(f"❌ Learning test error: {e}")
        return False

def test_feedback_learning():
    """Test if the system learns from user feedback"""
    print("\n👥 Testing Feedback Learning")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationFeedback
        
        service = TranslationService()
        
        # Test feedback learning
        test_text = "test learning phrase"
        test_translation = "test teduray phrase"
        
        print(f"🔍 Testing feedback learning with: '{test_text}' → '{test_translation}'")
        
        # Count initial feedback
        initial_feedback_count = TranslationFeedback.objects.count()
        print(f"📊 Initial feedback count: {initial_feedback_count}")
        
        # Submit positive feedback
        feedback = service.save_translation_feedback(
            source_text=test_text,
            source_lang="tagalog",
            target_lang="teduray",
            translated_text=test_translation,
            feedback_type="good"
        )
        
        if feedback:
            print(f"   ✅ Feedback saved: ID {feedback.id}")
            
            # Check if feedback count increased
            final_feedback_count = TranslationFeedback.objects.count()
            print(f"📊 Final feedback count: {final_feedback_count}")
            
            if final_feedback_count > initial_feedback_count:
                print(f"   ✅ FEEDBACK LEARNING WORKING! Added {final_feedback_count - initial_feedback_count} feedback records")
                
                # Check statistics
                stats = service.get_translation_statistics()
                feedback_received = stats['service_stats'].get('feedback_received', 0)
                print(f"   📊 Total feedback received: {feedback_received}")
                
                # Clean up test data
                feedback.delete()
                print(f"   🧹 Test feedback cleaned up")
                
                return True
            else:
                print(f"   ⚠️  Feedback count didn't increase")
                return False
        else:
            print(f"   ❌ Failed to save feedback")
            return False
            
    except Exception as e:
        print(f"❌ Feedback learning test error: {e}")
        return False

def test_database_growth():
    """Test if the database is actually growing with new patterns"""
    print("\n📈 Testing Database Growth")
    print("=" * 60)
    
    try:
        from translation_app.models import TranslationRule
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Count initial rules
        initial_count = TranslationRule.objects.filter(source_language='tagalog').count()
        print(f"📊 Initial database size: {initial_count} rules")
        
        # Test with words that might add to database
        test_words = ["kompyuter", "internet", "teknolohiya"]
        
        for word in test_words:
            print(f"\n🔍 Testing with: '{word}'")
            
            # Check if exists before
            exists_before = TranslationRule.objects.filter(
                source_language='tagalog',
                source_text__iexact=word
            ).exists()
            
            print(f"   Exists before: {exists_before}")
            
            # Translate
            result = service.translate(word, "tagalog", "teduray")
            print(f"   Translation: '{result}'")
            
            # Check if exists after
            exists_after = TranslationRule.objects.filter(
                source_language='tagalog',
                source_text__iexact=word
            ).exists()
            
            print(f"   Exists after: {exists_after}")
            
            if exists_after and not exists_before:
                print(f"   ✅ NEW RULE ADDED!")
            elif exists_after:
                print(f"   ℹ️  Rule already existed")
            else:
                print(f"   ⚠️  No rule added")
        
        # Count final rules
        final_count = TranslationRule.objects.filter(source_language='tagalog').count()
        print(f"\n📊 Final database size: {final_count} rules")
        
        growth = final_count - initial_count
        if growth > 0:
            print(f"   ✅ DATABASE GREW by {growth} rules!")
            return True
        else:
            print(f"   ℹ️  No new rules added (may already exist)")
            return False
            
    except Exception as e:
        print(f"❌ Database growth test error: {e}")
        return False

def test_learning_methods():
    """Test the specific learning methods"""
    print("\n🔬 Testing Learning Methods")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationRule
        
        service = TranslationService()
        
        # Test the learning method directly
        test_original = "test_learning_method"
        test_translation = "test_teduray_method"
        
        print(f"🔍 Testing learning method with: '{test_original}' → '{test_translation}'")
        
        # Check if exists before
        exists_before = TranslationRule.objects.filter(
            source_language='tagalog',
            source_text__iexact=test_original
        ).exists()
        
        print(f"   Exists before: {exists_before}")
        
        # Call learning method directly
        service._learn_from_authentic_translation(
            test_original, test_translation, "tagalog", "teduray"
        )
        
        # Check if exists after
        exists_after = TranslationRule.objects.filter(
            source_language='tagalog',
            source_text__iexact=test_original
        ).exists()
        
        print(f"   Exists after: {exists_after}")
        
        if exists_after and not exists_before:
            print(f"   ✅ LEARNING METHOD WORKS!")
            
            # Show the learned rule
            rule = TranslationRule.objects.filter(
                source_language='tagalog',
                source_text__iexact=test_original
            ).first()
            
            if rule:
                print(f"   📚 Learned rule: '{rule.source_text}' → '{rule.target_text}'")
            
            # Clean up
            rule.delete()
            print(f"   🧹 Test rule cleaned up")
            
            return True
        elif exists_after:
            print(f"   ℹ️  Rule already existed")
            return True
        else:
            print(f"   ❌ Learning method failed")
            return False
            
    except Exception as e:
        print(f"❌ Learning method test error: {e}")
        return False

def test_cache_learning():
    """Test if the system learns from cache interactions"""
    print("\n💾 Testing Cache Learning")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test cache learning
        test_text = "cache learning test"
        test_translation = "cache teduray test"
        
        print(f"🔍 Testing cache learning with: '{test_text}' → '{test_translation}'")
        
        # Cache a translation
        cached_result = service._cache_and_return(
            test_text, "tagalog", "teduray", test_translation,
            quality_score=0.9, source="test_cache"
        )
        
        print(f"   Cached result: '{cached_result}'")
        
        # Try to retrieve from cache
        retrieved = service.cache_manager.get_translation(test_text, "tagalog", "teduray")
        
        if retrieved:
            print(f"   ✅ CACHE LEARNING WORKS!")
            print(f"   📊 Cached translation: '{retrieved.get('translation', '')}'")
            print(f"   📊 Quality score: {retrieved.get('quality_score', 0)}")
            print(f"   📊 Source: {retrieved.get('source', '')}")
            return True
        else:
            print(f"   ❌ Cache learning failed")
            return False
            
    except Exception as e:
        print(f"❌ Cache learning test error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Learning System Test")
    print("=" * 70)
    print("Testing if the system is actually learning and improving")
    print("=" * 70)
    
    # Run learning tests
    translation_learning = test_learning_from_translations()
    feedback_learning = test_feedback_learning()
    database_growth = test_database_growth()
    learning_methods = test_learning_methods()
    cache_learning = test_cache_learning()
    
    print("\n" + "=" * 70)
    print("📊 LEARNING SYSTEM RESULTS")
    print("=" * 70)
    
    results = {
        "Translation Learning": translation_learning,
        "Feedback Learning": feedback_learning,
        "Database Growth": database_growth,
        "Learning Methods": learning_methods,
        "Cache Learning": cache_learning
    }
    
    for test_name, result in results.items():
        status = "✅ WORKING" if result else "❌ NEEDS WORK"
        print(f"{test_name}: {status}")
    
    working_count = sum(results.values())
    total_count = len(results)
    
    if working_count == total_count:
        print(f"\n🎉 ALL {total_count} LEARNING SYSTEMS WORKING!")
        print("The system IS learning and improving!")
        print("\n💡 Learning Features Active:")
        print("  • ✅ Learns from new translations")
        print("  • ✅ Learns from user feedback")
        print("  • ✅ Database grows with new patterns")
        print("  • ✅ Learning methods functional")
        print("  • ✅ Cache learning working")
        print("\n🧠 Your translation system:")
        print("  • Gets smarter over time")
        print("  • Remembers user corrections")
        print("  • Improves translation quality")
        print("  • Builds knowledge base")
    elif working_count >= total_count * 0.6:
        print(f"\n✅ MOSTLY LEARNING! {working_count}/{total_count} systems working")
        print("The system is learning but some features may need refinement")
    else:
        print(f"\n⚠️  LIMITED LEARNING: {working_count}/{total_count} systems working")
        print("The learning system needs improvement")
    
    print(f"\n🧠 Learning Status: {'ACTIVE' if working_count >= total_count * 0.6 else 'NEEDS WORK'}")
    print("=" * 70)
