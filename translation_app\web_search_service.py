"""
Professional Web Search Service for Translation
Provides intelligent web search capabilities with multiple search engines and sources.
"""

import requests
import time
import re
from urllib.parse import quote_plus
from django.conf import settings

try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False
    print("Warning: BeautifulSoup4 not available. Web scraping features will be limited.")


class WebSearchService:
    """Professional web search service with multiple search providers and intelligent result processing"""
    
    def __init__(self):
        self.serpapi_key = getattr(settings, 'SERPAPI_KEY', 'df39f706f7af4b3ac610d27b3ff8c23a1e3aa8765f574f59eece16cf86a41cdf')
        self.max_results_per_query = 5
        self.timeout = 10
        self.user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        
        # Specialized search sources for different types of content
        self.bible_sources = [
            'bible.com',
            'biblegateway.com',
            'biblehub.com'
        ]
        
        self.language_sources = [
            'ethnologue.com',
            'omniglot.com',
            'wikipedia.org'
        ]
        
        self.translation_sources = [
            'linguee.com',
            'reverso.net',
            'glosbe.com'
        ]
    
    def search_translation(self, text, source_lang, target_lang, search_type="general"):
        """
        Comprehensive translation search across multiple sources
        
        Args:
            text: Text to translate
            source_lang: Source language
            target_lang: Target language
            search_type: Type of search ("general", "bible", "linguistic", "cultural")
        
        Returns:
            List of search results with relevance scores
        """
        results = []
        
        try:
            # Generate search queries based on type
            queries = self._generate_search_queries(text, source_lang, target_lang, search_type)
            
            for query in queries:
                # Try SerpAPI first (most reliable)
                serpapi_results = self._search_with_serpapi(query)
                if serpapi_results:
                    results.extend(serpapi_results)
                
                # Fallback to direct Google search if SerpAPI fails
                if not serpapi_results:
                    google_results = self._search_with_google(query)
                    results.extend(google_results)
                
                # Add small delay to avoid rate limiting
                time.sleep(0.5)
            
            # Process and score results
            processed_results = self._process_and_score_results(results, text, source_lang, target_lang)
            
            # Remove duplicates and sort by relevance
            unique_results = self._deduplicate_results(processed_results)
            
            return sorted(unique_results, key=lambda x: x.get('relevance_score', 0), reverse=True)
            
        except Exception as e:
            print(f"Web search error: {e}")
            return []
    
    def _generate_search_queries(self, text, source_lang, target_lang, search_type):
        """Generate intelligent search queries based on context"""
        queries = []
        
        # Clean text for search
        clean_text = re.sub(r'[^\w\s]', '', text).strip()
        
        if search_type == "bible":
            queries.extend([
                f'"{text}" {source_lang} {target_lang} bible verse',
                f'"{clean_text}" Teduray Bible translation',
                f'"{clean_text}" Tagalog Bible verse Teduray',
                f'Bible verse "{text}" Teduray language'
            ])
        
        elif search_type == "linguistic":
            queries.extend([
                f'"{text}" Tagalog to Teduray translation dictionary',
                f'Teduray word for "{clean_text}"',
                f'"{clean_text}" Teduray language meaning translation',
                f'Teduray dictionary "{text}" Tagalog',
                f'"{text}" Teduray equivalent word',
                f'Maguindanao Teduray "{clean_text}" translation'
            ])
        
        elif search_type == "cultural":
            queries.extend([
                f'"{text}" Teduray culture meaning',
                f'"{clean_text}" Teduray cultural context',
                f'Teduray people "{text}" tradition',
                f'{source_lang} "{text}" cultural significance'
            ])
        
        else:  # general
            queries.extend([
                f'"{text}" Tagalog to Teduray translation',
                f'Teduray translation "{clean_text}"',
                f'"{clean_text}" Teduray word meaning',
                f'Teduray language dictionary "{text}"',
                f'"{text}" in Teduray language',
                f'Tagalog "{clean_text}" Teduray equivalent',
                f'Maguindanao Teduray "{text}" translation'
            ])
        
        return queries[:4]  # Limit to 4 queries to avoid rate limiting
    
    def _search_with_serpapi(self, query):
        """Search using SerpAPI (premium service)"""
        try:
            params = {
                "q": query,
                "api_key": self.serpapi_key,
                "engine": "google",
                "num": self.max_results_per_query,
                "hl": "en",
                "gl": "us"
            }
            
            response = requests.get("https://serpapi.com/search", params=params, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                results = []
                
                # Process organic results
                for item in data.get("organic_results", []):
                    results.append({
                        'title': item.get('title', ''),
                        'snippet': item.get('snippet', ''),
                        'url': item.get('link', ''),
                        'source': 'serpapi',
                        'query': query
                    })
                
                # Process answer box if available
                if "answer_box" in data:
                    answer_box = data["answer_box"]
                    results.append({
                        'title': answer_box.get('title', 'Answer Box'),
                        'snippet': answer_box.get('snippet', '') or answer_box.get('answer', ''),
                        'url': answer_box.get('link', ''),
                        'source': 'serpapi_answer',
                        'query': query
                    })
                
                return results
            
        except Exception as e:
            print(f"SerpAPI search failed: {e}")
        
        return []
    
    def _search_with_google(self, query):
        """Fallback search using direct Google search"""
        try:
            search_url = f"https://www.google.com/search?q={quote_plus(query)}"
            headers = {'User-Agent': self.user_agent}
            
            response = requests.get(search_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                if not HAS_BS4:
                    # Fallback: return basic result without HTML parsing
                    return [{
                        'title': f'Search result for: {query}',
                        'snippet': 'Web scraping not available (BeautifulSoup4 not installed)',
                        'url': '',
                        'source': 'google_direct_limited',
                        'query': query
                    }]

                soup = BeautifulSoup(response.text, 'html.parser')
                results = []

                # Extract search results
                for result in soup.find_all('div', class_='g')[:self.max_results_per_query]:
                    title_elem = result.find('h3')
                    snippet_elem = result.find('span', class_='aCOpRe') or result.find('div', class_='VwiC3b')
                    link_elem = result.find('a')

                    if title_elem and snippet_elem:
                        results.append({
                            'title': title_elem.get_text(strip=True),
                            'snippet': snippet_elem.get_text(strip=True),
                            'url': link_elem.get('href', '') if link_elem else '',
                            'source': 'google_direct',
                            'query': query
                        })

                return results
            
        except Exception as e:
            print(f"Google search failed: {e}")
        
        return []
    
    def _process_and_score_results(self, results, text, source_lang, target_lang):
        """Process and score search results for relevance"""
        processed_results = []
        
        for result in results:
            # Calculate relevance score
            relevance_score = self._calculate_relevance_score(result, text, source_lang, target_lang)
            
            # Extract potential translations from snippet
            translations = self._extract_translations_from_snippet(result['snippet'], text, target_lang)
            
            processed_result = {
                **result,
                'relevance_score': relevance_score,
                'extracted_translations': translations,
                'processed_snippet': self._clean_snippet(result['snippet'])
            }
            
            processed_results.append(processed_result)
        
        return processed_results
    
    def _calculate_relevance_score(self, result, text, source_lang, target_lang):
        """Calculate relevance score for a search result"""
        score = 0.0
        
        title = result.get('title', '').lower()
        snippet = result.get('snippet', '').lower()
        url = result.get('url', '').lower()
        
        # Check for exact text match
        if text.lower() in snippet or text.lower() in title:
            score += 0.4
        
        # Check for Teduray language mentions (high priority)
        if 'teduray' in snippet or 'teduray' in title:
            score += 0.4
        if 'maguindanao' in snippet or 'maguindanao' in title:
            score += 0.2  # Related language
        if source_lang.lower() in snippet or target_lang.lower() in snippet:
            score += 0.2

        # Check for translation-related keywords
        translation_keywords = ['translation', 'translate', 'meaning', 'dictionary', 'word', 'language', 'equivalent']
        for keyword in translation_keywords:
            if keyword in snippet or keyword in title:
                score += 0.1

        # Check for Bible-related content (often has good Teduray translations)
        bible_keywords = ['bible', 'verse', 'scripture', 'biblical']
        for keyword in bible_keywords:
            if keyword in snippet or keyword in title:
                score += 0.15

        # Check for linguistic/academic content
        academic_keywords = ['linguistic', 'grammar', 'phonology', 'morphology', 'syntax', 'indigenous']
        for keyword in academic_keywords:
            if keyword in snippet or keyword in title:
                score += 0.1
        
        # Boost score for trusted sources
        trusted_domains = self.bible_sources + self.language_sources + self.translation_sources
        for domain in trusted_domains:
            if domain in url:
                score += 0.2
                break
        
        # Penalize very short snippets
        if len(snippet) < 50:
            score -= 0.1
        
        return min(score, 1.0)
    
    def _extract_translations_from_snippet(self, snippet, original_text, target_lang):
        """Extract potential Teduray translations from search result snippet"""
        translations = []

        # Enhanced patterns for Teduray translation extraction
        patterns = [
            # Standard translation patterns
            rf'"{re.escape(original_text)}"[^"]*(?:means?|is|translates? to)[^"]*"([^"]+)"',
            rf'"{re.escape(original_text)}"[^"]*[:=][^"]*"([^"]+)"',
            rf'(?:means?|is|translates? to)[^"]*"([^"]+)"[^"]*"{re.escape(original_text)}"',

            # Teduray-specific patterns
            rf'teduray[^a-z]*(?:word|translation)[^a-z]*(?:for|of)[^a-z]*"{re.escape(original_text)}"[^a-z]*(?:is|means?)[^a-z]*"?([a-z]+)"?',
            rf'"{re.escape(original_text)}"[^a-z]*(?:in teduray|teduray)[^a-z]*(?:is|means?)[^a-z]*"?([a-z]+)"?',
            rf'teduray[^a-z]*"?([a-z]+)"?[^a-z]*(?:means?|for)[^a-z]*"{re.escape(original_text)}"',

            # Dictionary-style patterns
            rf'{re.escape(original_text)}[^a-z]*[-–—][^a-z]*([a-z]+)',
            rf'([a-z]+)[^a-z]*[-–—][^a-z]*{re.escape(original_text)}',

            # Bible verse patterns (often have good translations)
            rf'"{re.escape(original_text)}"[^"]*(?:verse|scripture)[^"]*teduray[^"]*"([^"]+)"',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, snippet, re.IGNORECASE)
            translations.extend(matches)

        # Also look for standalone Teduray words in the snippet
        if 'teduray' in snippet.lower():
            # Find words that might be Teduray translations
            words = re.findall(r'\b[a-z]{3,15}\b', snippet.lower())
            for word in words:
                # Skip common English/Tagalog words
                if word not in ['the', 'and', 'for', 'with', 'this', 'that', 'from', 'they', 'have', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'would', 'there', 'could', 'other', 'after', 'first', 'well', 'many', 'some', 'what', 'know', 'way', 'been', 'call', 'who', 'oil', 'sit', 'now', 'find', 'long', 'down', 'day', 'did', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'ang', 'mga', 'ako', 'siya', 'kami', 'kayo', 'sila', 'namin', 'natin', 'ninyo', 'nila']:
                    if len(word) >= 4:  # Teduray words are usually longer
                        translations.append(word)

        # Clean and filter translations
        cleaned_translations = []
        for trans in translations:
            trans = trans.strip()
            if len(trans) > 2 and len(trans) < 50:  # Reasonable length for Teduray words
                cleaned_translations.append(trans)

        # Remove duplicates while preserving order
        seen = set()
        unique_translations = []
        for trans in cleaned_translations:
            if trans.lower() not in seen:
                seen.add(trans.lower())
                unique_translations.append(trans)

        return unique_translations[:5]  # Return top 5 potential translations
    
    def _clean_snippet(self, snippet):
        """Clean and format snippet text"""
        # Remove extra whitespace and normalize
        cleaned = re.sub(r'\s+', ' ', snippet).strip()
        
        # Remove common search result artifacts
        cleaned = re.sub(r'\d+ days? ago', '', cleaned)
        cleaned = re.sub(r'Rating: \d+/\d+', '', cleaned)
        
        return cleaned
    
    def _deduplicate_results(self, results):
        """Remove duplicate results based on content similarity"""
        unique_results = []
        seen_snippets = set()
        
        for result in results:
            snippet = result.get('snippet', '')
            # Create a simple hash of the snippet for comparison
            snippet_hash = hash(snippet[:100])  # Use first 100 chars
            
            if snippet_hash not in seen_snippets:
                seen_snippets.add(snippet_hash)
                unique_results.append(result)
        
        return unique_results
    
    def search_bible_verse(self, text, source_lang="tagalog", target_lang="teduray"):
        """Specialized search for Bible verses"""
        return self.search_translation(text, source_lang, target_lang, search_type="bible")
    
    def search_linguistic_context(self, text, source_lang, target_lang):
        """Specialized search for linguistic context and grammar"""
        return self.search_translation(text, source_lang, target_lang, search_type="linguistic")
    
    def search_cultural_context(self, text, source_lang, target_lang):
        """Specialized search for cultural context and meaning"""
        return self.search_translation(text, source_lang, target_lang, search_type="cultural")
