"""
Attention-Enhanced Translation System
Implements attention mechanisms for improved translation quality and context understanding.
"""

import numpy as np
import re
from collections import defaultdict
from django.db.models import Q
from .models import TranslationRule


class AttentionTranslator:
    """
    Attention-based translation system that focuses on relevant context
    for better translation quality.
    """
    
    def __init__(self):
        self.attention_weights = {}
        self.context_memory = {}
        self.cultural_patterns = self._load_cultural_patterns()
        self.grammatical_patterns = self._load_grammatical_patterns()
        
    def translate_with_attention(self, text, source_lang, target_lang, context_rules=None):
        """
        Translate text using attention mechanisms to focus on relevant context.
        
        Args:
            text: Input text to translate
            source_lang: Source language
            target_lang: Target language
            context_rules: Additional context rules from database
            
        Returns:
            Dictionary with translation and attention information
        """
        # Tokenize and analyze input
        tokens = self._tokenize(text)
        
        # Calculate attention weights for each token
        attention_weights = self._calculate_attention_weights(
            tokens, source_lang, target_lang, context_rules
        )
        
        # Apply attention to generate context-aware translation
        translation = self._generate_attended_translation(
            tokens, attention_weights, source_lang, target_lang
        )
        
        # Calculate confidence score based on attention distribution
        confidence = self._calculate_confidence(attention_weights)
        
        return {
            'translation': translation,
            'attention_weights': attention_weights,
            'confidence': confidence,
            'focused_tokens': self._get_high_attention_tokens(tokens, attention_weights),
            'cultural_markers': self._identify_cultural_markers(tokens, source_lang)
        }
    
    def _tokenize(self, text):
        """Enhanced tokenization that preserves important linguistic features"""
        # Basic word tokenization with punctuation handling
        tokens = re.findall(r'\b\w+\b|[.!?;,]', text.lower())
        
        # Add position and context information
        enhanced_tokens = []
        for i, token in enumerate(tokens):
            enhanced_tokens.append({
                'text': token,
                'position': i,
                'is_punctuation': not token.isalnum(),
                'length': len(token),
                'context_window': tokens[max(0, i-2):min(len(tokens), i+3)]
            })
        
        return enhanced_tokens
    
    def _calculate_attention_weights(self, tokens, source_lang, target_lang, context_rules=None):
        """
        Calculate attention weights for each token based on multiple factors:
        1. Cultural significance
        2. Grammatical importance
        3. Translation difficulty
        4. Context relevance
        """
        weights = []
        
        for i, token in enumerate(tokens):
            weight = 0.1  # Base weight
            
            # Cultural significance attention
            cultural_weight = self._get_cultural_attention(token, source_lang)
            weight += cultural_weight * 0.3
            
            # Grammatical importance attention
            grammatical_weight = self._get_grammatical_attention(token, tokens, i, source_lang)
            weight += grammatical_weight * 0.25
            
            # Translation difficulty attention (higher for rare/complex words)
            difficulty_weight = self._get_difficulty_attention(token, source_lang, target_lang)
            weight += difficulty_weight * 0.2
            
            # Context relevance attention
            context_weight = self._get_context_attention(token, tokens, i, context_rules)
            weight += context_weight * 0.15
            
            # Position-based attention (beginning and end of sentences are important)
            position_weight = self._get_position_attention(i, len(tokens))
            weight += position_weight * 0.1
            
            weights.append(min(weight, 1.0))  # Cap at 1.0
        
        # Normalize weights to sum to 1
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        
        return weights
    
    def _get_cultural_attention(self, token, source_lang):
        """Calculate attention weight based on cultural significance"""
        cultural_markers = self.cultural_patterns.get(source_lang, {})
        
        token_text = token['text']
        
        # High attention for cultural/religious terms
        if token_text in cultural_markers.get('religious', []):
            return 0.9
        
        # Medium attention for family/social terms
        if token_text in cultural_markers.get('family', []):
            return 0.7
        
        # Medium attention for traditional concepts
        if token_text in cultural_markers.get('traditional', []):
            return 0.6
        
        return 0.1
    
    def _get_grammatical_attention(self, token, tokens, position, source_lang):
        """Calculate attention weight based on grammatical importance"""
        grammatical_markers = self.grammatical_patterns.get(source_lang, {})
        
        token_text = token['text']
        
        # High attention for verbs (action words)
        if token_text in grammatical_markers.get('verbs', []):
            return 0.8
        
        # High attention for question words
        if token_text in grammatical_markers.get('question_words', []):
            return 0.9
        
        # Medium attention for pronouns
        if token_text in grammatical_markers.get('pronouns', []):
            return 0.6
        
        # Check for compound constructions
        if position < len(tokens) - 1:
            next_token = tokens[position + 1]['text']
            compound = f"{token_text} {next_token}"
            if compound in grammatical_markers.get('compounds', []):
                return 0.7
        
        return 0.2
    
    def _get_difficulty_attention(self, token, source_lang, target_lang):
        """Calculate attention weight based on translation difficulty"""
        token_text = token['text']
        
        # Check if word exists in our translation database
        try:
            rules = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__icontains=token_text
            )
            
            if not rules.exists():
                # Unknown word - needs high attention
                return 0.8
            elif rules.count() == 1:
                # Single translation - medium attention
                return 0.4
            else:
                # Multiple possible translations - high attention needed
                return 0.7
                
        except Exception:
            return 0.5
    
    def _get_context_attention(self, token, tokens, position, context_rules):
        """Calculate attention weight based on surrounding context"""
        if not context_rules:
            return 0.1
        
        # Look at surrounding words
        context_window = 2
        start = max(0, position - context_window)
        end = min(len(tokens), position + context_window + 1)
        
        context_tokens = [t['text'] for t in tokens[start:end]]
        context_phrase = ' '.join(context_tokens)
        
        # Check if this context appears in our rules
        for rule in context_rules:
            if token['text'] in rule.source_text.lower():
                # This token appears in a known translation rule
                return 0.6
        
        return 0.2
    
    def _get_position_attention(self, position, total_length):
        """Calculate attention weight based on position in sentence"""
        # Beginning and end of sentences are typically more important
        if position == 0 or position == total_length - 1:
            return 0.3
        elif position == 1 or position == total_length - 2:
            return 0.2
        else:
            return 0.1
    
    def _generate_attended_translation(self, tokens, attention_weights, source_lang, target_lang):
        """Generate translation using attention weights to focus on important parts"""
        # Group tokens by attention level
        high_attention = []
        medium_attention = []
        low_attention = []
        
        for token, weight in zip(tokens, attention_weights):
            if weight > 0.6:
                high_attention.append(token)
            elif weight > 0.3:
                medium_attention.append(token)
            else:
                low_attention.append(token)
        
        # Translate high-attention tokens first with more context
        translation_parts = []
        
        # Focus on high-attention tokens
        for token in high_attention:
            translation = self._translate_with_focus(
                token, tokens, source_lang, target_lang, focus_level='high'
            )
            if translation:
                translation_parts.append(translation)
        
        # Handle medium-attention tokens
        for token in medium_attention:
            translation = self._translate_with_focus(
                token, tokens, source_lang, target_lang, focus_level='medium'
            )
            if translation:
                translation_parts.append(translation)
        
        # Handle low-attention tokens (simple translation)
        for token in low_attention:
            if not token['is_punctuation']:
                translation = self._translate_with_focus(
                    token, tokens, source_lang, target_lang, focus_level='low'
                )
                if translation:
                    translation_parts.append(translation)
        
        # Reconstruct sentence with proper grammar
        return self._reconstruct_sentence(translation_parts, target_lang)
    
    def _translate_with_focus(self, token, all_tokens, source_lang, target_lang, focus_level):
        """Translate a single token with different levels of contextual focus"""
        token_text = token['text']
        
        if focus_level == 'high':
            # Use maximum context for high-attention words
            context_size = 3
            context_start = max(0, token['position'] - context_size)
            context_end = min(len(all_tokens), token['position'] + context_size + 1)
            context = ' '.join([t['text'] for t in all_tokens[context_start:context_end]])
            
            # Look for phrase-level translations first
            rules = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__icontains=context[:50]  # Limit context length
            ).order_by('-source_text__length')  # Prefer longer matches
            
        elif focus_level == 'medium':
            # Use moderate context
            rules = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__icontains=token_text
            )
            
        else:  # low focus
            # Simple word-level translation
            rules = TranslationRule.objects.filter(
                source_language=source_lang,
                source_text__iexact=token_text
            )
        
        if rules.exists():
            return rules.first().target_text
        
        return token_text  # Return original if no translation found
    
    def _reconstruct_sentence(self, translation_parts, target_lang):
        """Reconstruct the translated sentence with proper grammar for target language"""
        if not translation_parts:
            return ""
        
        # Basic reconstruction - can be enhanced with language-specific grammar rules
        sentence = ' '.join(translation_parts)
        
        # Apply target language specific rules
        if target_lang.lower() == 'teduray':
            sentence = self._apply_teduray_grammar(sentence)
        elif target_lang.lower() == 'tagalog':
            sentence = self._apply_tagalog_grammar(sentence)
        
        return sentence.strip()
    
    def _apply_teduray_grammar(self, sentence):
        """Apply Teduray-specific grammatical rules"""
        # Add Teduray-specific grammar adjustments
        # This is a simplified version - can be expanded with more rules
        
        # Ensure proper word order (VSO - Verb Subject Object tendency)
        # Add proper markers like 'i', 'de', 'go', etc.
        
        return sentence
    
    def _apply_tagalog_grammar(self, sentence):
        """Apply Tagalog-specific grammatical rules"""
        # Add Tagalog-specific grammar adjustments
        return sentence
    
    def _calculate_confidence(self, attention_weights):
        """Calculate confidence score based on attention distribution"""
        if not attention_weights:
            return 0.0
        
        # Higher confidence when attention is well-distributed
        # Lower confidence when attention is too concentrated or too dispersed
        
        max_weight = max(attention_weights)
        min_weight = min(attention_weights)
        variance = np.var(attention_weights)
        
        # Ideal attention has some focus but not too concentrated
        if 0.3 <= max_weight <= 0.7 and variance < 0.1:
            return 0.9
        elif max_weight > 0.8:
            return 0.7  # Too concentrated
        elif max_weight < 0.2:
            return 0.5  # Too dispersed
        else:
            return 0.6
    
    def _get_high_attention_tokens(self, tokens, attention_weights):
        """Get tokens that received high attention"""
        high_attention = []
        for token, weight in zip(tokens, attention_weights):
            if weight > 0.5:
                high_attention.append({
                    'token': token['text'],
                    'weight': weight,
                    'position': token['position']
                })
        return high_attention
    
    def _identify_cultural_markers(self, tokens, source_lang):
        """Identify cultural markers in the text"""
        cultural_markers = []
        cultural_patterns = self.cultural_patterns.get(source_lang, {})
        
        for token in tokens:
            token_text = token['text']
            for category, words in cultural_patterns.items():
                if token_text in words:
                    cultural_markers.append({
                        'token': token_text,
                        'category': category,
                        'position': token['position']
                    })
        
        return cultural_markers
    
    def _load_cultural_patterns(self):
        """Load cultural patterns for different languages"""
        return {
            'tagalog': {
                'religious': ['diyos', 'panginoon', 'santo', 'santa', 'amen', 'hallelujah'],
                'family': ['ama', 'ina', 'anak', 'kuya', 'ate', 'lolo', 'lola', 'tito', 'tita'],
                'traditional': ['bayanihan', 'kapamilya', 'utang na loob', 'pakikipagkunware']
            },
            'teduray': {
                'religious': ['mekupu', 'kudarat', 'imam'],
                'family': ['bapa', 'ina', 'anak', 'kaka'],
                'traditional': ['adat', 'timuay', 'datu']
            }
        }
    
    def _load_grammatical_patterns(self):
        """Load grammatical patterns for different languages"""
        return {
            'tagalog': {
                'verbs': ['kumain', 'uminom', 'natulog', 'gumising', 'tumakbo', 'lumakad'],
                'pronouns': ['ako', 'ikaw', 'siya', 'kami', 'kayo', 'sila'],
                'question_words': ['ano', 'sino', 'saan', 'kailan', 'bakit', 'paano'],
                'compounds': ['kumusta ka', 'salamat po', 'paalam na']
            },
            'teduray': {
                'verbs': ['menama', 'minum', 'meturug', 'mebangon'],
                'pronouns': ['aku', 'ikew', 'siran', 'kami', 'kamu', 'siran'],
                'question_words': ['unu', 'sinew', 'diin', 'kanu', 'nguda'],
                'compounds': ['kumusta kew', 'salamat', 'paalam']
            }
        }
