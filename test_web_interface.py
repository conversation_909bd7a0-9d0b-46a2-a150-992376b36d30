#!/usr/bin/env python
"""
Test the web interface to see what's happening with the translation
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def test_web_interface_translation():
    """Test the exact same flow as the web interface"""
    print("🌐 Testing Web Interface Translation Flow")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        # Initialize service (same as web interface)
        service = TranslationService()
        
        # Clear any existing cache to start fresh
        try:
            service.cache_manager.clear_cache()
            print("✅ Cache cleared")
        except:
            print("⚠️  Could not clear cache")
        
        # Test the exact input from the web interface
        test_text = "Kumain ang matandang lalaki ng tinapay"
        source_lang = "tagalog"
        target_lang = "teduray"
        
        print(f"\n🔍 Testing: '{test_text}'")
        print(f"   Source: {source_lang}")
        print(f"   Target: {target_lang}")
        
        # Call the main translate method (same as web interface)
        result = service.translate(test_text, source_lang, target_lang)
        
        print(f"\n📤 Web Interface Result: '{result}'")
        
        # Now test each step manually to see where it goes wrong
        print(f"\n🔬 Manual Step-by-Step Analysis:")
        
        # Step 1: Cache
        cached = service.cache_manager.get_translation(test_text, source_lang, target_lang)
        if cached:
            print(f"   Step 1 - Cache: '{cached['translation']}'")
        else:
            print("   Step 1 - Cache: No hit")
        
        # Step 2: Bible
        bible_result = service._check_bible_corpus(test_text, source_lang, target_lang)
        if bible_result:
            print(f"   Step 2 - Bible: '{bible_result}'")
        else:
            print("   Step 2 - Bible: No match")
        
        # Step 3: translate.py
        py_result = service._check_translate_py(test_text, source_lang, target_lang)
        if py_result:
            print(f"   Step 3 - translate.py: '{py_result}'")
        else:
            print("   Step 3 - translate.py: No match")
        
        # Step 4: Database
        db_result = service._check_database_matches(test_text, source_lang, target_lang)
        is_simple = service._is_simple_text(test_text)
        is_high_quality = service._is_high_quality_match(test_text, db_result) if db_result else False
        
        print(f"   Step 4 - Database: '{db_result}' (simple: {is_simple}, high_quality: {is_high_quality})")
        
        # Step 5: Hierarchical
        hierarchical_result = service._hierarchical_translate(test_text, source_lang, target_lang)
        if hierarchical_result:
            print(f"   Step 5 - Hierarchical: '{hierarchical_result}'")
        else:
            print("   Step 5 - Hierarchical: No result")
        
        # Determine which step should win
        print(f"\n🎯 Analysis:")
        print(f"   Text length: {len(test_text.split())} words")
        print(f"   Is simple text: {is_simple}")
        print(f"   Should use hierarchical: {not is_simple}")
        
        if not is_simple:
            print(f"   ✅ Complex text should use hierarchical translation")
            if hierarchical_result:
                print(f"   ✅ Hierarchical result available: '{hierarchical_result}'")
                if result == hierarchical_result:
                    print(f"   ✅ Web interface used hierarchical result!")
                else:
                    print(f"   ❌ Web interface used different result: '{result}'")
            else:
                print(f"   ❌ No hierarchical result available")
        
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_vs_complex():
    """Test simple vs complex text handling"""
    print("\n📊 Testing Simple vs Complex Text Handling")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        test_cases = [
            ("kumain", "Single word (should use database)"),
            ("matandang lalaki", "Two words (should use database)"),
            ("Kumain ang lalaki", "Three words (should use hierarchical)"),
            ("Kumain ang matandang lalaki", "Four words (should use hierarchical)"),
            ("Kumain ang matandang lalaki ng tinapay", "Six words (should use hierarchical)"),
        ]
        
        for text, description in test_cases:
            print(f"\n🔍 {description}")
            print(f"   Text: '{text}'")
            
            is_simple = service._is_simple_text(text)
            result = service.translate(text, "tagalog", "teduray")
            
            print(f"   Is simple: {is_simple}")
            print(f"   Result: '{result}'")
            
            if is_simple:
                print(f"   ✅ Should use database/simple translation")
            else:
                print(f"   ✅ Should use hierarchical translation")
        
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_hierarchical_directly():
    """Test hierarchical translator directly"""
    print("\n🧠 Testing Hierarchical Translator Directly")
    print("=" * 50)
    
    try:
        from translation_app.hierarchical_translator import HierarchicalTranslator
        
        translator = HierarchicalTranslator()
        
        test_text = "Kumain ang matandang lalaki ng tinapay"
        
        result = translator.translate_hierarchically(test_text, "tagalog", "teduray")
        
        if result:
            print(f"✅ Hierarchical translation successful!")
            print(f"   Input: '{test_text}'")
            print(f"   Output: '{result['translation']}'")
            print(f"   Level: {result['level']}")
            print(f"   Method: {result['method']}")
            
            if 'phrases' in result:
                print(f"   Phrases detected:")
                for phrase in result['phrases']:
                    print(f"     - '{phrase['original']}' → '{phrase['translation']}' ({phrase['type']})")
        else:
            print(f"❌ Hierarchical translation failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Hierarchical test error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Web Interface Translation Test")
    print("=" * 60)
    
    # Run tests
    web_test = test_web_interface_translation()
    simple_test = test_simple_vs_complex()
    hierarchical_test = test_hierarchical_directly()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    if web_test:
        print("✅ Web Interface Test: PASSED")
    else:
        print("❌ Web Interface Test: FAILED")
    
    if simple_test:
        print("✅ Simple vs Complex Test: PASSED")
    else:
        print("❌ Simple vs Complex Test: FAILED")
    
    if hierarchical_test:
        print("✅ Hierarchical Direct Test: PASSED")
    else:
        print("❌ Hierarchical Direct Test: FAILED")
    
    if web_test and simple_test and hierarchical_test:
        print("\n🎉 ALL TESTS PASSED!")
        print("The hierarchical translation system should now work in the web interface!")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
    
    print("\n" + "=" * 60)
