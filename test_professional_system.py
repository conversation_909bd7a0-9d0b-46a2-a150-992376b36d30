#!/usr/bin/env python
"""
Test script for the Professional Translation System
Run this to verify that all components are working correctly.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

from translation_app.services import TranslationService

def test_professional_translation_system():
    """Test the professional translation system components"""
    print("🚀 Testing Professional Translation System")
    print("=" * 50)
    
    try:
        # Initialize the service
        print("1. Initializing TranslationService...")
        service = TranslationService()
        print("   ✅ Service initialized successfully")
        
        # Test basic translation
        print("\n2. Testing basic translation...")
        test_text = "Kumusta ka?"
        result = service.translate(test_text, 'tagalog', 'teduray')
        print(f"   Input: '{test_text}'")
        print(f"   Output: '{result}'")
        print("   ✅ Basic translation working")
        
        # Test quality scoring
        print("\n3. Testing quality scoring...")
        quality_data = service.quality_scorer.score_translation(
            test_text, result, 'tagalog', 'teduray', 'test'
        )
        print(f"   Quality Score: {quality_data['overall_score']:.3f}")
        print(f"   Quality Grade: {quality_data['grade']}")
        print("   ✅ Quality scoring working")
        
        # Test web search
        print("\n4. Testing web search...")
        try:
            search_results = service.web_search.search_translation(
                test_text, 'tagalog', 'teduray', 'general'
            )
            print(f"   Found {len(search_results)} search results")
            if search_results:
                print(f"   First result: {search_results[0].get('title', 'No title')[:50]}...")
            print("   ✅ Web search working")
        except Exception as e:
            print(f"   ⚠️  Web search failed (this is OK if no internet): {e}")
        
        # Test caching
        print("\n5. Testing intelligent caching...")
        cache_key = service.cache_manager.get_cache_key('translation', test_text, 'tagalog', 'teduray')
        print(f"   Cache key generated: {cache_key[:20]}...")
        
        # Cache a translation
        translation_data = {
            'translation': result,
            'quality_score': 0.8,
            'source': 'test'
        }
        service.cache_manager.set_translation(test_text, 'tagalog', 'teduray', translation_data)
        
        # Retrieve from cache
        cached_result = service.cache_manager.get_translation(test_text, 'tagalog', 'teduray')
        if cached_result:
            print(f"   Cached translation: '{cached_result['translation']}'")
            print("   ✅ Caching working")
        else:
            print("   ⚠️  Caching not working as expected")
        
        # Test statistics
        print("\n6. Testing statistics...")
        stats = service.get_translation_statistics()
        print(f"   Service stats: {stats['service_stats']}")
        print(f"   Cache hit rate: {stats['cache_stats'].get('hit_rate', 0)}%")
        print("   ✅ Statistics working")
        
        # Test translation memory
        print("\n7. Testing translation memory...")
        similar = service.translation_memory.get_similar_translations(
            test_text, 'tagalog', 'teduray', limit=3
        )
        print(f"   Found {len(similar)} similar translations")
        print("   ✅ Translation memory working")
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED! Professional Translation System is working correctly.")
        print("\nKey Features Verified:")
        print("✅ Professional translation pipeline")
        print("✅ Quality scoring system")
        print("✅ Web search integration")
        print("✅ Intelligent caching")
        print("✅ Performance statistics")
        print("✅ Translation memory")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """Test the API endpoints"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 30)

    try:
        from django.test import Client
        from django.conf import settings

        # Ensure testserver is in ALLOWED_HOSTS
        if 'testserver' not in settings.ALLOWED_HOSTS:
            settings.ALLOWED_HOSTS.append('testserver')

        client = Client()

        # Test translation API
        print("1. Testing translation API...")
        try:
            response = client.post('/api/translate/', {
                'text': 'Mahal kita',
                'source_lang': 'tagalog'
            })

            if response.status_code == 200:
                data = response.json()
                print(f"   Translation: '{data.get('translated_text', 'No translation')}'")
                print(f"   Quality Score: {data.get('quality_score', 'N/A')}")
                print("   ✅ Translation API working")
            else:
                print(f"   ❌ Translation API failed: {response.status_code}")
                if hasattr(response, 'content'):
                    print(f"   Response: {response.content.decode()[:200]}...")
        except Exception as e:
            print(f"   ❌ Translation API error: {e}")

        # Test statistics API
        print("\n2. Testing statistics API...")
        try:
            response = client.get('/api/statistics/')

            if response.status_code == 200:
                data = response.json()
                print(f"   Statistics loaded: {data.get('success', False)}")
                print("   ✅ Statistics API working")
            else:
                print(f"   ❌ Statistics API failed: {response.status_code}")
                if hasattr(response, 'content'):
                    print(f"   Response: {response.content.decode()[:200]}...")
        except Exception as e:
            print(f"   ❌ Statistics API error: {e}")

        print("\n✅ API endpoint testing completed")
        return True

    except Exception as e:
        print(f"\n❌ API TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Professional Translation System Test Suite")
    print("==========================================\n")
    
    # Run tests
    system_test = test_professional_translation_system()
    api_test = test_api_endpoints()
    
    print("\n" + "=" * 50)
    if system_test and api_test:
        print("🎉 ALL TESTS PASSED!")
        print("Your professional translation system is ready to use!")
        print("\nNext steps:")
        print("1. Run: python manage.py runserver")
        print("2. Open: http://127.0.0.1:8000/")
        print("3. Try the enhanced translation features!")
    else:
        print("❌ SOME TESTS FAILED")
        print("Please check the error messages above and fix any issues.")
    
    print("\n" + "=" * 50)
