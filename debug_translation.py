#!/usr/bin/env python
"""
Debug script to test what's happening with the translation system
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def debug_translation_service():
    """Debug the translation service to see what's happening"""
    print("🔍 Debugging Translation Service")
    print("=" * 40)
    
    try:
        from translation_app.services import TranslationService
        
        # Initialize service
        print("1. Initializing TranslationService...")
        service = TranslationService()
        print("   ✅ Service initialized")
        
        # Check if hierarchical translator is available
        print("\n2. Checking hierarchical translator...")
        if hasattr(service, 'hierarchical_translator'):
            print("   ✅ Hierarchical translator found")
        else:
            print("   ❌ Hierarchical translator NOT found")
            return False
        
        # Test the exact same input from the web interface
        test_text = "Kumain ang matandang lalaki ng tinapay"
        print(f"\n3. Testing: '{test_text}'")
        
        # Test hierarchical translation directly
        print("\n   a) Testing hierarchical translator directly...")
        try:
            hierarchical_result = service.hierarchical_translator.translate_hierarchically(
                test_text, "tagalog", "teduray"
            )
            if hierarchical_result:
                print(f"      Result: '{hierarchical_result['translation']}'")
                print(f"      Level: {hierarchical_result['level']}")
                print(f"      Method: {hierarchical_result['method']}")
            else:
                print("      No result from hierarchical translator")
        except Exception as e:
            print(f"      Error: {e}")
        
        # Test the main translate method
        print("\n   b) Testing main translate method...")
        try:
            main_result = service.translate(test_text, "tagalog", "teduray")
            print(f"      Result: '{main_result}'")
        except Exception as e:
            print(f"      Error: {e}")
        
        # Test the hierarchical method in the service
        print("\n   c) Testing _hierarchical_translate method...")
        try:
            if hasattr(service, '_hierarchical_translate'):
                hierarchical_service_result = service._hierarchical_translate(
                    test_text, "tagalog", "teduray"
                )
                print(f"      Result: '{hierarchical_service_result}'")
            else:
                print("      _hierarchical_translate method not found")
        except Exception as e:
            print(f"      Error: {e}")
        
        # Check translation statistics
        print("\n4. Checking translation statistics...")
        try:
            stats = service.get_translation_statistics()
            print(f"   Total requests: {stats['service_stats']['total_requests']}")
            print(f"   Cache hits: {stats['service_stats']['cache_hits']}")
            print(f"   Gemini calls: {stats['service_stats']['gemini_calls']}")
        except Exception as e:
            print(f"   Error getting stats: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_translation():
    """Test the specific translation that's not working"""
    print("\n🎯 Testing Specific Translation")
    print("=" * 40)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test the exact input from the screenshot
        test_cases = [
            "Kumain ang matandang lalaki ng tinapay",
            "matandang lalaki",
            "kumain ng tinapay",
            "Kumusta ka?"
        ]
        
        for i, text in enumerate(test_cases, 1):
            print(f"\n{i}. Testing: '{text}'")
            
            try:
                # Test each step of the pipeline
                print("   Pipeline steps:")
                
                # Step 1: Cache check
                cached = service.cache_manager.get_translation(text, "tagalog", "teduray")
                if cached:
                    print(f"   ✅ Cache hit: '{cached['translation']}'")
                    continue
                else:
                    print("   ⚪ No cache hit")
                
                # Step 2: Bible corpus
                bible_result = service._check_bible_corpus(text, "tagalog", "teduray")
                if bible_result:
                    print(f"   ✅ Bible match: '{bible_result}'")
                    continue
                else:
                    print("   ⚪ No Bible match")
                
                # Step 3: Translate.py
                py_result = service._check_translate_py(text, "tagalog", "teduray")
                if py_result:
                    print(f"   ✅ Translate.py match: '{py_result}'")
                    continue
                else:
                    print("   ⚪ No translate.py match")
                
                # Step 4: Database
                db_result = service._check_database_matches(text, "tagalog", "teduray")
                if db_result:
                    print(f"   ✅ Database match: '{db_result}'")
                    continue
                else:
                    print("   ⚪ No database match")
                
                # Step 5: Hierarchical
                hierarchical_result = service._hierarchical_translate(text, "tagalog", "teduray")
                if hierarchical_result:
                    print(f"   ✅ Hierarchical result: '{hierarchical_result}'")
                    continue
                else:
                    print("   ⚪ No hierarchical result")
                
                # Final result
                final_result = service.translate(text, "tagalog", "teduray")
                print(f"   🎯 Final result: '{final_result}'")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def check_database_content():
    """Check what's in the database"""
    print("\n📊 Checking Database Content")
    print("=" * 40)
    
    try:
        from translation_app.models import TranslationRule
        
        # Check total rules
        total_rules = TranslationRule.objects.count()
        print(f"Total translation rules: {total_rules}")
        
        # Check by language
        tagalog_rules = TranslationRule.objects.filter(source_language='tagalog').count()
        teduray_rules = TranslationRule.objects.filter(source_language='teduray').count()
        
        print(f"Tagalog → Teduray rules: {tagalog_rules}")
        print(f"Teduray → Tagalog rules: {teduray_rules}")
        
        # Check for specific words
        test_words = ['kumain', 'lalaki', 'matanda', 'tinapay']
        print(f"\nChecking specific words:")
        
        for word in test_words:
            rule = TranslationRule.objects.filter(
                source_language='tagalog',
                source_text__iexact=word
            ).first()
            
            if rule:
                print(f"   '{word}' → '{rule.target_text}' ✅")
            else:
                print(f"   '{word}' → Not found ❌")
        
        # Show some sample rules
        print(f"\nSample rules:")
        sample_rules = TranslationRule.objects.filter(source_language='tagalog')[:5]
        for rule in sample_rules:
            print(f"   '{rule.source_text}' → '{rule.target_text}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Database check error: {e}")
        return False

if __name__ == "__main__":
    print("🐛 Translation System Debug")
    print("=" * 50)
    
    # Run debug tests
    service_test = debug_translation_service()
    translation_test = test_specific_translation()
    database_test = check_database_content()
    
    print("\n" + "=" * 50)
    print("📊 DEBUG RESULTS")
    print("=" * 50)
    
    if service_test:
        print("✅ Service Debug: PASSED")
    else:
        print("❌ Service Debug: FAILED")
    
    if translation_test:
        print("✅ Translation Test: PASSED")
    else:
        print("❌ Translation Test: FAILED")
    
    if database_test:
        print("✅ Database Check: PASSED")
    else:
        print("❌ Database Check: FAILED")
    
    print("\n" + "=" * 50)
