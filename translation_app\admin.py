from django.contrib import admin
from .models import TranslationRule, TranslationHistory, TranslationCorrection, TranslationFeedback

@admin.register(TranslationRule)
class TranslationRuleAdmin(admin.ModelAdmin):
    list_display = ('source_language', 'source_text', 'target_text')
    list_filter = ('source_language',)
    search_fields = ('source_text', 'target_text')

@admin.register(TranslationHistory)
class TranslationHistoryAdmin(admin.ModelAdmin):
    list_display = ('source_language', 'source_text_preview', 'translated_text_preview', 'timestamp')
    list_filter = ('source_language', 'timestamp')
    search_fields = ('source_text', 'translated_text')
    
    def source_text_preview(self, obj):
        return obj.source_text[:50] + '...' if len(obj.source_text) > 50 else obj.source_text
    
    def translated_text_preview(self, obj):
        return obj.translated_text[:50] + '...' if len(obj.translated_text) > 50 else obj.translated_text
    
    source_text_preview.short_description = 'Source Text'
    translated_text_preview.short_description = 'Translated Text'

@admin.register(TranslationCorrection)
class TranslationCorrectionAdmin(admin.ModelAdmin):
    list_display = ('source_language', 'target_language', 'source_text_preview', 'original_translation_preview', 'corrected_translation_preview', 'correction_type', 'is_verified', 'timestamp')
    list_filter = ('source_language', 'target_language', 'correction_type', 'is_verified', 'timestamp')
    search_fields = ('source_text', 'original_translation', 'corrected_translation')
    readonly_fields = ('timestamp',)
    list_editable = ('is_verified',)
    actions = ['mark_as_verified', 'create_translation_rules']

    def source_text_preview(self, obj):
        return obj.source_text[:50] + '...' if len(obj.source_text) > 50 else obj.source_text
    source_text_preview.short_description = 'Source Text'

    def original_translation_preview(self, obj):
        return obj.original_translation[:50] + '...' if len(obj.original_translation) > 50 else obj.original_translation
    original_translation_preview.short_description = 'Original Translation'

    def corrected_translation_preview(self, obj):
        return obj.corrected_translation[:50] + '...' if len(obj.corrected_translation) > 50 else obj.corrected_translation
    corrected_translation_preview.short_description = 'Corrected Translation'

    def mark_as_verified(self, request, queryset):
        queryset.update(is_verified=True)
        self.message_user(request, f"{queryset.count()} corrections marked as verified.")
    mark_as_verified.short_description = "Mark selected corrections as verified"

    def create_translation_rules(self, request, queryset):
        count = 0
        for correction in queryset.filter(is_verified=True):
            TranslationRule.objects.update_or_create(
                source_language=correction.source_language,
                source_text=correction.source_text,
                defaults={'target_text': correction.corrected_translation}
            )
            count += 1
        self.message_user(request, f"{count} translation rules created from verified corrections.")
    create_translation_rules.short_description = "Create translation rules from verified corrections"

@admin.register(TranslationFeedback)
class TranslationFeedbackAdmin(admin.ModelAdmin):
    list_display = ('source_language', 'target_language', 'source_text_preview', 'translated_text_preview', 'feedback_type', 'timestamp')
    list_filter = ('source_language', 'target_language', 'feedback_type', 'timestamp')
    search_fields = ('source_text', 'translated_text')
    readonly_fields = ('timestamp',)

    def source_text_preview(self, obj):
        return obj.source_text[:50] + '...' if len(obj.source_text) > 50 else obj.source_text
    source_text_preview.short_description = 'Source Text'

    def translated_text_preview(self, obj):
        return obj.translated_text[:50] + '...' if len(obj.translated_text) > 50 else obj.translated_text
    translated_text_preview.short_description = 'Translated Text'