#!/usr/bin/env python
"""
Analyze our translation system against authentic Teduray grammar patterns
"""

import os
import django
import re
from collections import defaultdict, Counter

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'translation_project.settings')
django.setup()

def analyze_teduray_grammar_patterns():
    """Analyze authentic Teduray grammar patterns from our data sources"""
    print("🔍 Analyzing Authentic Teduray Grammar Patterns")
    print("=" * 60)
    
    try:
        from translation_app.services import TranslationService
        from translation_app.models import TranslationRule
        
        service = TranslationService()
        
        # Get all Teduray translations from our data sources
        print("📊 Data Sources Analysis:")
        
        # 1. Database rules
        db_rules = TranslationRule.objects.filter(source_language='tagalog')
        teduray_from_db = [rule.target_text for rule in db_rules if rule.target_text]
        print(f"   Database rules: {len(teduray_from_db)} Teduray translations")
        
        # 2. translate.py data
        teduray_from_py = []
        for entry in service.translate_py_data:
            if entry.get('target') and 'teduray' in str(entry.get('target_lang', '')).lower():
                teduray_from_py.append(entry['target'])
        print(f"   translate.py: {len(teduray_from_py)} Teduray translations")
        
        # 3. Bible data (if available)
        bible_teduray = []
        try:
            bible_data = service._get_bible_data()
            for verse in bible_data:
                if verse.get('teduray'):
                    bible_teduray.append(verse['teduray'])
            print(f"   Bible data: {len(bible_teduray)} Teduray verses")
        except:
            print(f"   Bible data: Not available")
        
        # Combine all authentic Teduray text
        all_teduray_text = teduray_from_db + teduray_from_py + bible_teduray
        print(f"   Total authentic Teduray samples: {len(all_teduray_text)}")
        
        return all_teduray_text
        
    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return []

def analyze_grammatical_markers(teduray_texts):
    """Analyze grammatical markers in authentic Teduray text"""
    print("\n🔤 Grammatical Markers Analysis")
    print("=" * 50)
    
    # Markers we're currently using
    our_markers = ['i', 'de', 'nu', 'kun', 'go']
    
    # Find all potential markers in authentic text
    marker_patterns = defaultdict(list)
    marker_contexts = defaultdict(list)
    
    for text in teduray_texts:
        if not text:
            continue
            
        words = text.lower().split()
        
        # Analyze each word as potential marker
        for i, word in enumerate(words):
            if len(word) <= 3 and word.isalpha():  # Potential markers are short
                # Get context (word before and after)
                before = words[i-1] if i > 0 else ""
                after = words[i+1] if i < len(words)-1 else ""
                
                marker_patterns[word].append(text)
                marker_contexts[word].append(f"{before} [{word}] {after}")
    
    # Analyze frequency and usage
    print("📊 Marker Frequency Analysis:")
    marker_freq = Counter()
    for marker, occurrences in marker_patterns.items():
        marker_freq[marker] = len(occurrences)
    
    # Show top markers
    for marker, count in marker_freq.most_common(20):
        status = "✅ USING" if marker in our_markers else "❓ NOT USING"
        print(f"   '{marker}': {count} occurrences {status}")
    
    print("\n🔍 Our Current Markers Analysis:")
    for marker in our_markers:
        count = marker_freq.get(marker, 0)
        if count > 0:
            print(f"   ✅ '{marker}': {count} authentic occurrences")
            # Show some contexts
            contexts = marker_contexts[marker][:3]
            for context in contexts:
                print(f"      Example: {context}")
        else:
            print(f"   ⚠️  '{marker}': No occurrences found in authentic data")
    
    return marker_freq, marker_contexts

def analyze_word_order_patterns(teduray_texts):
    """Analyze word order patterns in authentic Teduray"""
    print("\n📝 Word Order Pattern Analysis")
    print("=" * 50)
    
    # Look for verb-subject-object vs subject-verb-object patterns
    verb_patterns = []
    sentence_structures = []
    
    # Common Teduray verbs to identify
    teduray_verbs = [
        'menama', 'minum', 'meturug', 'melakad', 'melayag', 'mebangon',
        'malago', 'mefiya', 'melalag', 'metutud', 'mekelaw', 'meketey'
    ]
    
    for text in teduray_texts:
        if not text or len(text.split()) < 3:
            continue
            
        words = text.lower().split()
        
        # Find sentences with verbs
        for verb in teduray_verbs:
            if verb in words:
                verb_index = words.index(verb)
                sentence_structure = {
                    'text': text,
                    'verb': verb,
                    'verb_position': verb_index,
                    'total_words': len(words),
                    'words': words
                }
                verb_patterns.append(sentence_structure)
                
                # Analyze structure
                if verb_index == 0:
                    sentence_structures.append('VSO')
                elif verb_index == 1 and len(words) > 2:
                    sentence_structures.append('SVO')
                else:
                    sentence_structures.append('OTHER')
    
    # Analyze patterns
    structure_counts = Counter(sentence_structures)
    print("📊 Sentence Structure Patterns:")
    for structure, count in structure_counts.items():
        percentage = (count / len(sentence_structures)) * 100 if sentence_structures else 0
        print(f"   {structure}: {count} occurrences ({percentage:.1f}%)")
    
    print("\n🔍 Example Verb Patterns:")
    for pattern in verb_patterns[:10]:
        pos_desc = "Verb-initial" if pattern['verb_position'] == 0 else f"Verb at position {pattern['verb_position']}"
        print(f"   '{pattern['text']}' - {pos_desc}")
    
    return structure_counts, verb_patterns

def analyze_our_translation_accuracy():
    """Test our current translation system against authentic patterns"""
    print("\n🧪 Our Translation System Accuracy Test")
    print("=" * 50)
    
    try:
        from translation_app.services import TranslationService
        
        service = TranslationService()
        
        # Test sentences that should reveal grammar issues
        test_cases = [
            ("Kumain ang lalaki", "Simple SVO sentence"),
            ("Masaya ang bata", "Adjective predicate"),
            ("Natulog sa bahay", "Verb with location"),
            ("Kumain ng tinapay ang bata", "Complex sentence with object"),
            ("Maganda ang dalaga", "Adjective + noun"),
            ("Uminom ng tubig", "Verb + object"),
            ("Sa bahay natulog", "Location-fronted"),
            ("Ang bata ay kumain", "Marked subject"),
        ]
        
        print("🔍 Testing Our Current Grammar Implementation:")
        
        for tagalog, description in test_cases:
            result = service.translate(tagalog, "tagalog", "teduray")
            print(f"\n   Test: {description}")
            print(f"   Input: '{tagalog}'")
            print(f"   Output: '{result}'")
            
            # Analyze the output
            words = result.split()
            markers_used = [w for w in words if w in ['i', 'de', 'nu', 'kun', 'go']]
            print(f"   Markers used: {markers_used}")
            
            # Check for potential issues
            issues = []
            if 'ang' in result:
                issues.append("Still contains Tagalog 'ang'")
            if 'ng' in result:
                issues.append("Still contains Tagalog 'ng'")
            if 'sa' in result:
                issues.append("Still contains Tagalog 'sa'")
            
            if issues:
                print(f"   ⚠️  Issues: {', '.join(issues)}")
            else:
                print(f"   ✅ No obvious Tagalog remnants")
        
        return True
        
    except Exception as e:
        print(f"❌ Translation test error: {e}")
        return False

def analyze_verb_conjugation_patterns(teduray_texts):
    """Analyze verb conjugation patterns in authentic Teduray"""
    print("\n🔄 Verb Conjugation Pattern Analysis")
    print("=" * 50)
    
    # Look for verb prefixes and patterns
    verb_prefixes = defaultdict(list)
    verb_roots = defaultdict(list)
    
    for text in teduray_texts:
        if not text:
            continue
            
        words = text.lower().split()
        
        for word in words:
            # Look for potential verb patterns
            if len(word) > 3:
                # Check for common prefixes
                prefixes = ['me', 'ne', 'te', 'ke', 'pe', 'se', 'le']
                for prefix in prefixes:
                    if word.startswith(prefix):
                        root = word[len(prefix):]
                        if len(root) > 2:
                            verb_prefixes[prefix].append(word)
                            verb_roots[root].append(word)
    
    print("📊 Verb Prefix Analysis:")
    for prefix, verbs in verb_prefixes.items():
        if len(verbs) >= 3:  # Only show prefixes with multiple examples
            print(f"   '{prefix}-': {len(verbs)} verbs")
            examples = list(set(verbs))[:5]
            print(f"      Examples: {', '.join(examples)}")
    
    print("\n🔍 Our Current Verb Handling:")
    our_verb_mappings = {
        'kumain': 'menama',
        'uminom': 'minum', 
        'natulog': 'meturug',
        'masaya': 'mefiya',
        'mamahal': 'malago'
    }
    
    for tagalog, teduray in our_verb_mappings.items():
        print(f"   {tagalog} → {teduray}")
        
        # Check if our mapping appears in authentic data
        found_in_data = any(teduray in text.lower() for text in teduray_texts if text)
        status = "✅ AUTHENTIC" if found_in_data else "❓ VERIFY"
        print(f"      Status: {status}")

def generate_grammar_recommendations(marker_freq, structure_counts):
    """Generate recommendations for improving our grammar"""
    print("\n💡 Grammar Improvement Recommendations")
    print("=" * 50)
    
    recommendations = []
    
    # Marker recommendations
    print("🔤 Marker Usage Recommendations:")
    
    # Check if we're missing important markers
    important_markers = [marker for marker, count in marker_freq.most_common(10) 
                        if count > 10 and marker not in ['i', 'de', 'nu', 'kun', 'go']]
    
    if important_markers:
        print("   📝 Consider adding these frequent markers:")
        for marker in important_markers[:5]:
            count = marker_freq[marker]
            print(f"      '{marker}': {count} occurrences in authentic data")
            recommendations.append(f"Add '{marker}' marker support")
    
    # Word order recommendations
    print("\n📝 Word Order Recommendations:")
    if structure_counts:
        dominant_structure = structure_counts.most_common(1)[0]
        print(f"   Dominant pattern: {dominant_structure[0]} ({dominant_structure[1]} occurrences)")
        
        if dominant_structure[0] == 'VSO':
            print("   ✅ Teduray appears to prefer Verb-Subject-Object order")
            recommendations.append("Prioritize VSO word order in translations")
        elif dominant_structure[0] == 'SVO':
            print("   ✅ Teduray appears to prefer Subject-Verb-Object order")
            recommendations.append("Prioritize SVO word order in translations")
    
    # General recommendations
    print("\n🎯 General Recommendations:")
    general_recs = [
        "Validate all grammatical markers against authentic Teduray corpus",
        "Implement more sophisticated verb conjugation patterns",
        "Add context-sensitive marker selection",
        "Improve noun phrase structure handling",
        "Enhance conditional sentence patterns"
    ]
    
    for i, rec in enumerate(general_recs, 1):
        print(f"   {i}. {rec}")
        recommendations.append(rec)
    
    return recommendations

if __name__ == "__main__":
    print("🔬 Teduray Grammar Authenticity Analysis")
    print("=" * 70)
    print("Analyzing our translation system against authentic Teduray patterns")
    print("=" * 70)
    
    # Step 1: Get authentic Teduray data
    teduray_texts = analyze_teduray_grammar_patterns()
    
    if not teduray_texts:
        print("❌ No Teduray data available for analysis")
        exit(1)
    
    # Step 2: Analyze grammatical markers
    marker_freq, marker_contexts = analyze_grammatical_markers(teduray_texts)
    
    # Step 3: Analyze word order patterns
    structure_counts, verb_patterns = analyze_word_order_patterns(teduray_texts)
    
    # Step 4: Analyze verb conjugations
    analyze_verb_conjugation_patterns(teduray_texts)
    
    # Step 5: Test our current system
    analyze_our_translation_accuracy()
    
    # Step 6: Generate recommendations
    recommendations = generate_grammar_recommendations(marker_freq, structure_counts)
    
    print("\n" + "=" * 70)
    print("📊 ANALYSIS SUMMARY")
    print("=" * 70)
    
    print(f"✅ Analyzed {len(teduray_texts)} authentic Teduray text samples")
    print(f"✅ Identified {len(marker_freq)} unique grammatical markers")
    print(f"✅ Analyzed {len(verb_patterns)} verb pattern examples")
    print(f"✅ Generated {len(recommendations)} improvement recommendations")
    
    print("\n🎯 Next Steps:")
    print("1. Review the marker usage patterns above")
    print("2. Validate our current grammatical markers against authentic data")
    print("3. Adjust word order preferences based on dominant patterns")
    print("4. Implement missing important markers")
    print("5. Enhance verb conjugation accuracy")
    
    print("\n" + "=" * 70)
