# Generated by Django 5.2 on 2025-06-03 16:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('translation_app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TranslationCorrection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_language', models.CharField(max_length=20)),
                ('target_language', models.CharField(max_length=20)),
                ('source_text', models.TextField()),
                ('original_translation', models.TextField()),
                ('corrected_translation', models.TextField()),
                ('correction_type', models.CharField(choices=[('correction', 'User Correction'), ('addition', 'New Translation'), ('improvement', 'Improvement Suggestion')], default='correction', max_length=20)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('is_verified', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='TranslationFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_language', models.CharField(max_length=20)),
                ('target_language', models.CharField(max_length=20)),
                ('source_text', models.TextField()),
                ('translated_text', models.TextField()),
                ('feedback_type', models.CharField(choices=[('good', 'Good Translation'), ('bad', 'Bad Translation'), ('partial', 'Partially Correct')], max_length=20)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
